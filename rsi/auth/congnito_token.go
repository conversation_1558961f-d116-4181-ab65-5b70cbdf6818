package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/coreos/go-oidc/v3/oidc"
	"golang.org/x/oauth2"
)

const (
	// Cognito configuration
	CognitoProviderURL = "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_mkh5sGdqx"
	ClientID           = "3pff4gmpd43om04visj8k6nmil"
)

// TokenManager handles OAuth2 token operations
type TokenManager struct {
	config   *oauth2.Config
	provider *oidc.Provider
}

// NewTokenManager creates a new token manager
func NewTokenManager(ctx context.Context) (*TokenManager, error) {
	provider, err := oidc.NewProvider(ctx, CognitoProviderURL)
	if err != nil {
		return nil, fmt.Errorf("failed to create OIDC provider: %w", err)
	}

	config := &oauth2.Config{
		ClientID:     ClientID,
		ClientSecret: "",
		Endpoint:     provider.Endpoint(),
		Scopes:       []string{oidc.ScopeOpenID, "email", "profile"},
	}

	return &TokenManager{
		config:   config,
		provider: provider,
	}, nil
}

// TokenInfo represents token status information
type TokenInfo struct {
	Valid           bool          `json:"valid"`
	Expired         bool          `json:"expired"`
	ExpiresAt       time.Time     `json:"expiresAt"`
	TimeRemaining   time.Duration `json:"timeRemaining"`
	HasRefreshToken bool          `json:"hasRefreshToken"`
	NeedsRefresh    bool          `json:"needsRefresh"`
}

// GetTokenInfo returns information about a token
func (tm *TokenManager) GetTokenInfo(token *oauth2.Token) TokenInfo {
	now := time.Now()
	timeRemaining := token.Expiry.Sub(now)
	needsRefresh := timeRemaining < 5*time.Minute // Refresh if less than 5 minutes remaining

	return TokenInfo{
		Valid:           token.Valid(),
		Expired:         !token.Valid(),
		ExpiresAt:       token.Expiry,
		TimeRemaining:   timeRemaining,
		HasRefreshToken: token.RefreshToken != "",
		NeedsRefresh:    needsRefresh,
	}
}

// RefreshToken refreshes an OAuth2 token using its refresh token
func (tm *TokenManager) RefreshToken(ctx context.Context, token *oauth2.Token) (*oauth2.Token, error) {
	if token.RefreshToken == "" {
		return nil, fmt.Errorf("no refresh token available")
	}

	tokenSource := tm.config.TokenSource(ctx, token)
	newToken, err := tokenSource.Token()
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}

	return newToken, nil
}

// RefreshIfNeeded refreshes a token only if it's expired or expiring soon
func (tm *TokenManager) RefreshIfNeeded(ctx context.Context, token *oauth2.Token) (*oauth2.Token, bool, error) {
	info := tm.GetTokenInfo(token)

	if !info.NeedsRefresh && info.Valid {
		return token, false, nil
	}

	if !info.HasRefreshToken {
		return nil, false, fmt.Errorf("token expired and no refresh token available")
	}

	newToken, err := tm.RefreshToken(ctx, token)
	if err != nil {
		return nil, false, err
	}

	return newToken, true, nil
}

// LoadTokenFromFile loads a token from a JSON file
func LoadTokenFromFile(filename string) (*oauth2.Token, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read token file %s: %w", filename, err)
	}

	var token oauth2.Token
	if err := json.Unmarshal(data, &token); err != nil {
		return nil, fmt.Errorf("failed to parse token JSON: %w", err)
	}

	return &token, nil
}

// SaveTokenToFile saves a token to a JSON file
func SaveTokenToFile(token *oauth2.Token, filename string) error {
	data, err := json.MarshalIndent(token, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal token to JSON: %w", err)
	}

	if err := os.WriteFile(filename, data, 0600); err != nil {
		return fmt.Errorf("failed to write token to file %s: %w", filename, err)
	}

	return nil
}

// EnsureValidToken loads a token from file, refreshes it if needed, and saves it back
func (tm *TokenManager) EnsureValidToken(ctx context.Context, filename string) (*oauth2.Token, error) {
	token, err := LoadTokenFromFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to load token: %w", err)
	}

	newToken, refreshed, err := tm.RefreshIfNeeded(ctx, token)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh token: %w", err)
	}

	if refreshed {
		if err := SaveTokenToFile(newToken, filename); err != nil {
			return nil, fmt.Errorf("failed to save refreshed token: %w", err)
		}
	}

	return newToken, nil
}

// AutoRefreshTokenSource creates a token source that automatically refreshes tokens
func (tm *TokenManager) AutoRefreshTokenSource(ctx context.Context, token *oauth2.Token) oauth2.TokenSource {
	return tm.config.TokenSource(ctx, token)
}
