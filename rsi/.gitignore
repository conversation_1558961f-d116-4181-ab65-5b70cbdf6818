# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
rsi

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Go workspace file
go.work

# Dependency directories (remove the comment below to include it)
# vendor/

# IDE files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Local environment files
.env

# Dist directory
dist/