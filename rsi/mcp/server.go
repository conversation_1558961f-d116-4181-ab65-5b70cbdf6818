package mcp

import (
	"context"
	"fmt"
	"strings"

	"github.com/mark3labs/mcp-go/server"
	"github.com/rudderlabs/rs-ai/rsi/api"
	"github.com/rudderlabs/rs-ai/rsi/mcp/analytic"
	"github.com/rudderlabs/rs-ai/rsi/mcp/tools"
	"github.com/rudderlabs/rs-ai/rsi/profile"
)

type MCPServer struct {
	token     string
	cpClient  *api.Client
	workspace *api.Workspace

	profile   profile.Profile
	adminAuth api.Auth
}

func NewMCPServer(p profile.Profile) *MCPServer {
	cpClient := api.NewClient(api.PATAuth(p.Token))

	return &MCPServer{
		token:     p.Token,
		cpClient:  cpClient,
		workspace: cpClient.ForWorkspace(p.WorkspaceID),
		profile:   p,
		adminAuth: api.NewAdminJWT(p.AdminRefreshToken),
	}
}

func (m *MCPServer) Run() error {
	auth := api.PATAuth(m.token)
	client := api.NewClient(auth)
	userDetails, err := client.GetUserDetails(context.Background())
	if err != nil {
		return fmt.Errorf("failed to get user details: %v", err)
	}

	a := analytic.Default().WithUser(userDetails.ID, userDetails.Email)
	s := server.NewMCPServer("Rudder MCP Server", "1.0.0", a.ServerHook())

	// Add workspace tools for current workspace
	workspaceTools := tools.WorkspaceTools{
		Workspace: m.workspace,
	}

	// Add user tools
	userTools := &tools.UserTools{
		Client:   client,
		CPClient: m.cpClient,
		SetWorkspace: func(workspace *api.Workspace) {
			workspaceTools.Workspace = workspace
		},
	}

	for _, tool := range userTools.Tools() {
		s.AddTool(tool.Tool, tool.Handler)
	}

	for _, tool := range workspaceTools.Tools() {
		s.AddTool(tool.Tool, tool.Handler)
	}

	// Add admin tools only for RudderStack employees
	if strings.HasSuffix(userDetails.Email, "@rudderstack.com") && m.profile.AdminRefreshToken != "" {
		adminClient := api.NewAdminClient(userDetails.Email, m.adminAuth, api.FromEnv("MCP"))
		adminTools := &tools.AdminTools{
			AdminClient: adminClient,
			SetWorkspace: func(workspace *api.Workspace) {
				workspaceTools.Workspace = workspace
			},
		}

		// Add all admin tools to the server
		for _, tool := range adminTools.Tools() {
			s.AddTool(tool.Tool, tool.Handler)
		}
	}

	return server.ServeStdio(s)
}
