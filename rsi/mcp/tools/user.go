package tools

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/rudderlabs/rs-ai/rsi/api"
)

type UserTools struct {
	Client       *api.Client
	CPClient     *api.Client
	SetWorkspace func(*api.Workspace)
}

func (u *UserTools) Tools() (tools []server.ServerTool) {

	// Tool: user_details
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"user_details",
			mcp.WithDescription("Get name, email, workspaces, organizations of the user"),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			userDetails, err := u.Client.GetUserDetails(ctx)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to get user details: %v", err)), nil
			}
			data, err := json.MarshalIndent(userDetails, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal user details: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: switch_workspace
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"user_switch_workspace",
			mcp.WithDescription("Switch to a different workspace accessible to the user. CAREFUL! List options using the `user_details` tool and ALWAYS MUST ASK FOR USER CONFIRMATION BEFORE SWITCHING WORKSPACES."),
			mcp.WithString("workspace_id",
				mcp.Required(),
				mcp.Description("ID of the workspace to switch to"),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			workspaceID, ok := req.Params.Arguments["workspace_id"].(string)
			if !ok || workspaceID == "" {
				return mcp.NewToolResultError("workspace_id parameter is required"), nil
			}

			workspace := u.CPClient.ForWorkspace(workspaceID)
			if u.SetWorkspace != nil {
				u.SetWorkspace(workspace)
			}

			return mcp.NewToolResultText(fmt.Sprintf("Workspace set to %q", workspaceID)), nil
		},
	})

	return tools
}
