package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/rudderlabs/rs-ai/rsi/api"
	"golang.org/x/sync/errgroup"
)

type AdminTools struct {
	AdminClient  *api.AdminClient
	SetWorkspace func(*api.Workspace)
}

type WorkspaceWithTenant struct {
	Workspace  api.AdminWorkspace `json:"workspace"`
	TenantInfo *api.OrgTenantInfo `json:"tenantInfo,omitempty"`
}

func (a *AdminTools) Tools() (tools []server.ServerTool) {

	// SearchCriteria maps user-friendly search terms to API SearchBy values
	var searchCriteriaMap = map[string]api.SearchBy{
		"workspaceId":         api.SearchByWorkspaceID,     // Search by workspace ID
		"kubernetesNamespace": api.SearchByNamespace,       // Search by workspace name/namespace
		"memberEmail":         api.SearchByEmail,           // Search by user's email
		"organizationId":      api.SearchByOrgID,           // Search by organization ID
		"sourceId":            api.SearchBySourceID,        // Search by source ID
		"sourceType":          api.SearchBySourceType,      // Search by source type
		"destinationId":       api.SearchByDestinationID,   // Search by destination ID
		"destinationType":     api.SearchByDestinationType, // Search by destination type
	}

	enumValues := make([]string, 0, len(searchCriteriaMap))
	for k := range searchCriteriaMap {
		enumValues = append(enumValues, k)
	}

	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"admin_search_workspaces",
			mcp.WithDescription("Search for workspaces using various criteria. This tool provides admin-level search capabilities across all workspaces. "),
			mcp.WithString("search_by",
				mcp.Required(),
				mcp.Description("The field to search by. userMail and kubernetesNamespace allow for fuzzy matching. userMail usually contains organization domain (e.g. <EMAIL>)"),
				mcp.Enum(enumValues...),
			),
			mcp.WithString("value",
				mcp.Required(),
				mcp.Description("The value to search for based on the selected search criteria. "),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			searchBy, _ := req.Params.Arguments["search_by"].(string)
			if searchBy == "" {
				return mcp.NewToolResultError("search_by parameter is required"), nil
			}

			value, _ := req.Params.Arguments["value"].(string)
			if value == "" {
				return mcp.NewToolResultError("value parameter is required"), nil
			}

			// Convert user-friendly search criteria to API SearchBy type
			searchByEnum, ok := searchCriteriaMap[searchBy]
			if !ok {
				return mcp.NewToolResultError(fmt.Sprintf("Invalid search_by: %s", searchBy)), nil
			}

			workspaces, err := a.AdminClient.SearchWorkspaces(searchByEnum, value)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to search workspaces: %v", err)), nil
			}
			if len(workspaces) == 0 {
				return mcp.NewToolResultText(fmt.Sprintf("No workspaces found matching the search criteria: %s = %s", searchBy, value)), nil
			}

			billingClient := api.NewBillingClient()
			g := errgroup.Group{}
			var mu sync.Mutex

			tenantInfos := make(map[string]*api.OrgTenantInfo)

			for _, workspace := range workspaces {
				if workspace.Status != "ACTIVE" {
					continue
				}

				g.Go(func() error {
					tenantInfo, err := billingClient.OrgTenantInfo(workspace.OrganizationID)
					if err != nil {
						// ignore errors for now
						return nil
					}

					mu.Lock()
					tenantInfos[workspace.OrganizationID] = tenantInfo
					mu.Unlock()

					return nil
				})
			}

			// Wait for all goroutines to complete
			if err := g.Wait(); err != nil {
				// Log the error but continue with partial results
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal workspaces: %v", err)), nil
			}

			// Combine workspace and tenant info
			result := make([]WorkspaceWithTenant, len(workspaces))
			for i, workspace := range workspaces {
				result[i] = WorkspaceWithTenant{
					Workspace:  workspace,
					TenantInfo: tenantInfos[workspace.OrganizationID],
				}
			}

			data, err := json.MarshalIndent(result, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal workspaces: %v", err)), nil
			}

			return mcp.NewToolResultText(string(data)), nil
		},
	})

	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"admin_impersonate_workspace",
			mcp.WithDescription("Impersonate a workspace using admin credentials. This allows performing operations as if you were a member of that workspace. CAREFUL! Ensure you have selected the correct workspace before using this tool."),
			mcp.WithString("workspace_id",
				mcp.Required(),
				mcp.Description("ID of the workspace to impersonate"),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			workspaceID, _ := req.Params.Arguments["workspace_id"].(string)
			if workspaceID == "" {
				return mcp.NewToolResultError("workspace_id parameter is required"), nil
			}

			// Get the impersonated workspace client
			workspace, err := a.AdminClient.ImpersonateWorkspace(workspaceID)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to impersonate workspace: %v", err)), nil
			}

			// Update the workspace using the callback
			if a.SetWorkspace != nil {
				a.SetWorkspace(workspace)
			}

			return mcp.NewToolResultText(fmt.Sprintf("Successfully impersonated workspace %q for the duration of this session", workspaceID)), nil
		},
	})

	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"list_billing_plans",
			mcp.WithDescription("List all available billing plans. Returns plan details including features, pricing, and configuration."),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			billingClient := api.NewBillingClient()

			plans, err := billingClient.Plans()
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to fetch billing plans: %v", err)), nil
			}

			if len(plans) == 0 {
				return mcp.NewToolResultText("No billing plans found"), nil
			}

			// Sort plans by name for consistent output
			sort.Slice(plans, func(i, j int) bool {
				return plans[i].Name < plans[j].Name
			})

			// Create a more structured response
			type PlanResponse struct {
				TotalPlans int        `json:"totalPlans"`
				Plans      []api.Plan `json:"plans"`
				PlanTypes  []string   `json:"planTypes"` // List of unique plan types
			}

			// Extract unique plan types
			planTypesMap := make(map[string]bool)
			for _, plan := range plans {
				if planType, ok := plan.Features["PLAN_TYPE"].(string); ok {
					planTypesMap[planType] = true
				}
			}
			planTypes := make([]string, 0, len(planTypesMap))
			for planType := range planTypesMap {
				planTypes = append(planTypes, planType)
			}
			sort.Strings(planTypes)

			response := PlanResponse{
				TotalPlans: len(plans),
				Plans:      plans,
				PlanTypes:  planTypes,
			}

			data, err := json.MarshalIndent(response, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal plans: %v", err)), nil
			}

			return mcp.NewToolResultText(string(data)), nil
		},
	})

	return tools
}
