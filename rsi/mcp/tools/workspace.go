package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"sort"
	"strconv"
	"time"

	"github.com/aereal/jsondiff"
	"github.com/itchyny/gojq"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/rudderlabs/rs-ai/rsi/api"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"
)

// MCPAuditLogEntry represents an audit log entry in MCP format
type MCPAuditLogEntry struct {
	ID         string          `json:"id"`
	UserName   string          `json:"userName"`
	UserID     string          `json:"userId"`
	IP         string          `json:"ip"`
	Target     string          `json:"target"`
	TargetType string          `json:"targetType"`
	Workspace  string          `json:"workspace"`
	Action     string          `json:"action"`
	CreatedAt  time.Time       `json:"createdAt"`
	Diff       json.RawMessage `json:"diff"`
}

type WorkspaceTools struct {
	Workspace *api.Workspace
}

// processAuditLog processes a single audit log entry and returns the MCP format
func processAuditLog(log api.AuditLogEntry) (*MCPAuditLogEntry, error) {
	// Create diff from payload using jsondiff
	var diff string
	if len(log.Payload.From) > 0 || len(log.Payload.To) > 0 {
		// Parse the from/to JSON into interface{}
		var fromObj, toObj interface{}
		if len(log.Payload.From) > 0 {
			if err := json.Unmarshal(log.Payload.From, &fromObj); err != nil {
				return nil, fmt.Errorf("failed to parse from payload: %v", err)
			}
		}
		if len(log.Payload.To) > 0 {
			if err := json.Unmarshal(log.Payload.To, &toObj); err != nil {
				return nil, fmt.Errorf("failed to parse to payload: %v", err)
			}
		}

		// Create gojq query to ignore specific fields from diff:
		// - createdAt: timestamps that always change
		// - id/ID: any form of ID field (lowercase or uppercase)
		// - *Id/*ID fields: any field ending with Id/ID (e.g. userId, workspaceId)
		// - *_id/*_ID fields: any field ending with _id/_ID
		// - updatedAt: update timestamps
		// - versionId: version identifiers
		// - destinationDefinition, sourceDefinition: large definition objects
		// - empty objects and null values
		query, err := gojq.Parse(`
			walk(
				if type == "object" then
					with_entries(
						select(
							(.key | 
							ascii_downcase | 
							(
								contains("id") or
								. == "createdat" or
								. == "updatedat" or
								. == "versionid" or
								. == "destinationdefinition" or
								. == "sourcedefinition"
							) | not) and
							(.value != null) and
							(
								if .value | type == "object" then
									.value | length > 0
								else 
									true
								end
							)
						)
					)
				elif . == null then
					empty
				else
					.
				end
			)
		`)
		if err != nil {
			return nil, fmt.Errorf("failed to create ignore query: %v", err)
		}

		// Compute diff using jsondiff with ignore option
		diffStr, err := jsondiff.DiffFromObjects(fromObj, toObj, jsondiff.Only(query))
		if err != nil {
			return nil, fmt.Errorf("failed to compute diff: %v", err)
		}

		// Truncate diff if longer than 1000 characters
		if len(diffStr) > 1000 {
			diff = diffStr[:997] + "..."
		} else {
			diff = diffStr
		}
	}

	return &MCPAuditLogEntry{
		ID:         log.ID,
		UserName:   log.User.Name,
		UserID:     log.UserID,
		IP:         log.IP,
		Target:     log.Target,
		TargetType: log.TargetType,
		Workspace:  log.Workspace,
		Action:     log.Action,
		CreatedAt:  log.CreatedAt,
		Diff:       json.RawMessage(fmt.Sprintf("%q", diff)), // Convert to JSON string
	}, nil
}

func (w *WorkspaceTools) Tools() (tools []server.ServerTool) {

	// Tool: list_sources
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"list_sources",
			mcp.WithDescription("List all sources in the workspace"),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			sources, err := w.Workspace.ListSources()
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to list sources: %v", err)), nil
			}
			data, err := json.MarshalIndent(sources, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal sources: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: list_destinations
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"list_destinations",
			mcp.WithDescription("List all destinations in the workspace"),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			destinations, err := w.Workspace.ListDestinations()
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to list destinations: %v", err)), nil
			}
			data, err := json.MarshalIndent(destinations, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal destinations: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: send_event
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"send_event",
			mcp.WithDescription("Send a demo track event to sources"),
			mcp.WithArray("write_keys",
				mcp.Required(),
				mcp.MinItems(1),
				mcp.Description("multiple write_keys of the sources to send the event to, you may use `list_sources` to get the write_key"),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			writeKeys, err := cast.ToStringSliceE(req.Params.Arguments["write_keys"])
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to cast write_keys to string slice: %v", err)), nil
			}

			if len(writeKeys) == 0 {
				return mcp.NewToolResultError("write_keys parameter cannot be empty"), nil
			}

			// Get workspace settings to get data plane URL
			settings, err := w.Workspace.WorkspaceSettings()
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to get workspace settings: %v", err)), nil
			}

			// Validate write keys
			for i, writeKey := range writeKeys {
				if writeKey == "" {
					return mcp.NewToolResultError(fmt.Sprintf("Empty write key at position %d", i)), nil
				}
			}

			// Create context with timeout
			ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
			defer cancel()

			// Create errgroup for concurrent event sending
			g, gctx := errgroup.WithContext(ctx)

			for _, writeKey := range writeKeys {
				g.Go(func() error {
					// Create dataplane client for this write key
					dataplaneClient := api.NewDataplaneClient(settings.DataPlaneURL, writeKey)

					// Send the event
					err := dataplaneClient.SendEvent(gctx)
					if err != nil {
						err = fmt.Errorf("failed to send event: %w", err)
					}
					return err
				})
			}

			// Wait for all goroutines to complete
			err = g.Wait()
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to send events: %v", err)), nil
			}

			return mcp.NewToolResultText(fmt.Sprintf("Successfully sent events to %d destinations", len(writeKeys))), nil
		},
	})

	// Tool: health_metrics
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"health_metrics",
			mcp.WithDescription("Get health metrics for all destinations in the last 24 hours"),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			healthMetrics, err := w.Workspace.StreamHealthMetrics(ctx, time.Now().Add(-24*time.Hour), time.Now())
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to fetch health metrics: %v", err)), nil
			}

			// Get destinations for name mapping
			destinations, err := w.Workspace.ListDestinations()
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to list destinations: %v", err)), nil
			}

			// Create name mapping
			destDetails := make(map[string]api.Destination)
			for _, dest := range destinations {
				destDetails[dest.ID] = dest
			}

			// Enhance metrics with destination names
			type EnhancedMetric struct {
				DestinationID      string          `json:"destinationId"`
				DestinationDetails api.Destination `json:"destinationDetails"`
				SuccessSum         int64           `json:"successSum"`
				AbortSum           int64           `json:"abortSum"`
				ErrorRate          float64         `json:"errorRate"`
				IsProblematic      bool            `json:"isProblematic"`
			}

			errorThreshold := 0.1 // default threshold
			if threshold, ok := req.Params.Arguments["error_threshold"].(float64); ok {
				errorThreshold = threshold
			}

			enhancedMetrics := make([]EnhancedMetric, 0)
			for _, metric := range healthMetrics {
				totalEvents := metric.SuccessSum + metric.AbortSum
				var errorRate float64
				if totalEvents > 0 {
					errorRate = float64(metric.AbortSum) / float64(totalEvents)
				}

				isProblematic := errorRate >= errorThreshold && totalEvents > 0

				enhancedMetrics = append(enhancedMetrics, EnhancedMetric{
					DestinationID:      metric.DestinationID,
					DestinationDetails: destDetails[metric.DestinationID],
					SuccessSum:         metric.SuccessSum,
					AbortSum:           metric.AbortSum,
					ErrorRate:          errorRate,
					IsProblematic:      isProblematic,
				})
			}

			// Sort by error rate (highest first)
			sort.Slice(enhancedMetrics, func(i, j int) bool {
				return enhancedMetrics[i].ErrorRate > enhancedMetrics[j].ErrorRate
			})

			data, err := json.MarshalIndent(enhancedMetrics, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal metrics: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: stream_errors
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"stream_errors",
			mcp.WithDescription("Get event stream errors for a specific destination"),
			mcp.WithString("destination_id",
				mcp.Required(),
				mcp.Description("ID of the destination to fetch errors for"),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			destinationID, ok := req.Params.Arguments["destination_id"].(string)
			if !ok || destinationID == "" {
				return mcp.NewToolResultError("destination_id parameter is required"), nil
			}

			errors, err := w.Workspace.DestinationEventErrors(ctx, destinationID, time.Now().Add(-24*time.Hour))
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to fetch event stream errors: %v", err)), nil
			}

			data, err := json.MarshalIndent(errors, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal errors: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: sample_error_event
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"sample_error_event",
			mcp.WithDescription("Get a sample event for a specific error, you can get the error details using the `stream_errors` tool"),
			mcp.WithString("source_id",
				mcp.Required(),
				mcp.Description("ID of the source"),
			),
			mcp.WithString("destination_id",
				mcp.Required(),
				mcp.Description("ID of the destination"),
			),
			mcp.WithString("event_name",
				mcp.Required(),
				mcp.Description("Name of the event that errored"),
			),
			mcp.WithString("event_type",
				mcp.Required(),
				mcp.Description("Type of the event that errored"),
			),
			mcp.WithString("status_code",
				mcp.Required(),
				mcp.Description("Status code of the error"),
			),
			mcp.WithString("reported_at",
				mcp.Required(),
				mcp.Description("Time when the error was reported (RFC3339 format)"),
			),
			mcp.WithString("reported_by",
				mcp.Required(),
				mcp.Description("Entity that reported the error"),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			sourceID, ok := req.Params.Arguments["source_id"].(string)
			if !ok || sourceID == "" {
				return mcp.NewToolResultError("source_id parameter is required"), nil
			}

			destinationID, ok := req.Params.Arguments["destination_id"].(string)
			if !ok || destinationID == "" {
				return mcp.NewToolResultError("destination_id parameter is required"), nil
			}

			eventName, ok := req.Params.Arguments["event_name"].(string)
			if !ok || eventName == "" {
				return mcp.NewToolResultError("event_name parameter is required"), nil
			}

			eventType, ok := req.Params.Arguments["event_type"].(string)
			if !ok || eventType == "" {
				return mcp.NewToolResultError("event_type parameter is required"), nil
			}

			statusCode, ok := req.Params.Arguments["status_code"].(string)
			if !ok || statusCode == "" {
				return mcp.NewToolResultError("status_code parameter is required"), nil
			}
			statusCodeInt, err := strconv.Atoi(statusCode)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Invalid status code: %v", err)), nil
			}

			reportedAt, ok := req.Params.Arguments["reported_at"].(string)
			if !ok || reportedAt == "" {
				return mcp.NewToolResultError("reported_at parameter is required"), nil
			}
			reportedAtTime, err := time.Parse(time.RFC3339, reportedAt)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Invalid reported_at time format (should be RFC3339): %v", err)), nil
			}

			reportedBy, ok := req.Params.Arguments["reported_by"].(string)
			if !ok || reportedBy == "" {
				return mcp.NewToolResultError("reported_by parameter is required"), nil
			}

			streamError := api.EventStreamError{
				SourceID:      sourceID,
				DestinationID: destinationID,
				EventName:     eventName,
				EventType:     eventType,
				StatusCode:    statusCodeInt,
				ReportedAt:    reportedAtTime,
				ReportedBy:    reportedBy,
			}

			sample, err := w.Workspace.GetSampleEvent(ctx, streamError)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to fetch sample event: %v", err)), nil
			}

			type SampleEventResponse struct {
				Event    string `json:"event"`
				Response string `json:"response"`
				Error    string `json:"error,omitempty"`
			}

			sampleResponse := SampleEventResponse{
				Event:    sample.SampleEvent,
				Response: sample.SampleResponse,
			}

			data, err := json.MarshalIndent(sampleResponse, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal sample event: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: list_connections
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"list_connections",
			mcp.WithDescription("List all connections between sources and destinations in the workspace"),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			connections, err := w.Workspace.ListConnections()
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to list connections: %v", err)), nil
			}
			data, err := json.MarshalIndent(connections, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal connections: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: transformation_test_new
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"transformation_test_new",
			mcp.WithDescription("Test a new transformation by sending code, input, and options to the API. Returns transformed events and logs."),
			mcp.WithString("code",
				mcp.Required(),
				mcp.Description("Transformation code to test. Use the `sample_transformations` tool to get a starting point."),
				mcp.DefaultString(`
				export function transformEvent(event, metadata) {
					const meta = metadata(event);
					event.sourceId = meta.sourceId;

					log("Event Name is", event.event, ";", "Message ID is", event.messageId);
					log("Source ID is", meta.sourceId);

					return event;
				}
			`),
			),
			mcp.WithString("input",
				mcp.Required(),
				mcp.Description("Input event as JSON string, you pass an array of rudderstack events"),
			),
			mcp.WithString("language",
				mcp.Description("Language of the transformation code (e.g., 'javascript')"),
				mcp.DefaultString("javascript"),
				mcp.Enum("javascript", "python"),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			code, _ := req.Params.Arguments["code"].(string)
			input, _ := req.Params.Arguments["input"].(string)
			language, _ := req.Params.Arguments["language"].(string)

			if language == "" {
				language = "javascript"
			}

			transReq := api.TransformationTestRequest{
				Code:                  code,
				CodeVersion:           "1",
				Input:                 input,
				Language:              language,
				UpgradeTransformation: false,
			}

			resp, err := w.Workspace.TransformationTest(ctx, transReq)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to test transformation: %v", err)), nil
			}
			data, err := json.MarshalIndent(resp, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal transformation response: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: transformation_test_existing
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"transformation_test_existing",
			mcp.WithDescription("Test an existing transformation by sending input events. Returns transformed events and logs. Prefer this tool over `transformation_test` when you want to test an existing transformation."),
			mcp.WithString("transformation_id",
				mcp.Required(),
				mcp.Description("ID of the transformation to test, instead of passing the code you can pass the transformation id"),
			),
			mcp.WithString("input",
				mcp.Required(),
				mcp.Description("Input event as JSON string, you pass an array of rudderstack events"),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			code, _ := req.Params.Arguments["code"].(string)
			input, _ := req.Params.Arguments["input"].(string)
			language, _ := req.Params.Arguments["language"].(string)
			transformationID, _ := req.Params.Arguments["transformation_id"].(string)

			if transformationID != "" {
				transformation, err := w.Workspace.GetTransformation(ctx, transformationID)
				if err != nil {
					return mcp.NewToolResultError(fmt.Sprintf("Failed to get transformation: %v", err)), nil
				}
				code = transformation.Code
				language = transformation.Language
			}

			transReq := api.TransformationTestRequest{
				Code:                  code,
				CodeVersion:           "1",
				Input:                 input,
				Language:              language,
				UpgradeTransformation: false,
			}

			resp, err := w.Workspace.TransformationTest(ctx, transReq)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to test transformation: %v", err)), nil
			}
			data, err := json.MarshalIndent(resp, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal transformation response: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: sample_transformations
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"sample_transformations",
			mcp.WithDescription("List sample transformations provided by RudderStack as starting points for new transformations."),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			resp, err := w.Workspace.SampleTransformations(ctx)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to fetch sample transformations: %v", err)), nil
			}
			// Filter for LLM: id, name, description, code, language, category
			type LLMTransformation struct {
				ID          string `json:"id"`
				Name        string `json:"name"`
				Description string `json:"description"`
				Code        string `json:"code"`
				Language    string `json:"language"`
				Category    string `json:"category"`
			}
			llmList := make([]LLMTransformation, 0, len(resp))
			for _, t := range resp {
				llmList = append(llmList, LLMTransformation{
					ID:          t.ID,
					Name:        t.Name,
					Description: t.Description,
					Code:        t.Code,
					Language:    t.Language,
					Category:    t.Category,
				})
			}
			data, err := json.MarshalIndent(llmList, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal sample transformations: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: upsert_transformation
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"upsert_transformation",
			mcp.WithDescription("Upsert a transformation to RudderStack"),
			mcp.WithString("id",
				mcp.Description("ID of the transformation to update (for upsert)"),
			),
			mcp.WithString("name",
				mcp.Required(),
				mcp.Description("Name of the transformation"),
			),
			mcp.WithString("description",
				mcp.Description("Description of the transformation"),
			),
			mcp.WithString("code",
				mcp.Required(),
				mcp.Description("Transformation code"),
			),
			mcp.WithString("language",
				mcp.Description("Language of the transformation (default: javascript)"),
				mcp.DefaultString("javascript"),
				mcp.Enum("javascript", "python"),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			name, _ := req.Params.Arguments["name"].(string)
			description, _ := req.Params.Arguments["description"].(string)
			code, _ := req.Params.Arguments["code"].(string)
			language, _ := req.Params.Arguments["language"].(string)
			id, _ := req.Params.Arguments["id"].(string)

			reqBody := api.SaveTransformationRequest{
				ID:          id,
				Name:        name,
				Description: description,
				Code:        code,
				WorkspaceID: w.Workspace.ID(),
				Language:    language,
			}

			resp, err := w.Workspace.SaveTransformation(ctx, reqBody)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to save transformation: %v", err)), nil
			}

			mcpResponse := struct {
				VersionID string `json:"transformationVersionId"`
				ID        string `json:"transformationId"`
			}{
				VersionID: resp.VersionID,
				ID:        resp.ID,
			}
			data, err := json.MarshalIndent(mcpResponse, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal save transformation response: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: connect_transformation_destination
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"connect_transformation_destination",
			mcp.WithDescription("Connect a transformation to a destination, both the transformation and destination must already exist. You can only connect one transformation to one destination. Ensure you are not overwriting an existing transformation logic using the`get_transformation` or `list_transformations` tool. Try to combine the logic"),
			mcp.WithString("transformation_id",
				mcp.Required(),
				mcp.Description("ID of the transformation to connect"),
			),
			mcp.WithString("destination_id",
				mcp.Required(),
				mcp.Description("ID of the destination to connect to, you can get the destination id from the `list_destinations` tool"),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			transformationID, _ := req.Params.Arguments["transformation_id"].(string)
			if transformationID == "" {
				return mcp.NewToolResultError("transformation_id parameter is required"), nil
			}

			destinationID, _ := req.Params.Arguments["destination_id"].(string)
			if destinationID == "" {
				return mcp.NewToolResultError("destination_id parameter is required"), nil
			}

			transformations, err := w.Workspace.ListTransformations(ctx)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to list transformations: %v", err)), nil
			}

			for _, t := range transformations {
				if slices.Contains(t.DestinationIDs, destinationID) {
					return mcp.NewToolResultError(fmt.Sprintf("Destination %q is already connected to transformation %q. Try to merge the logic in the existing transformation `%q` .", destinationID, t.ID, t.ID)), nil
				}
			}

			// Call the API client's ConnectTransformationToDestination method
			err = w.Workspace.ConnectTransformationToDestination(
				ctx,
				transformationID,
				destinationID,
			)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to connect transformation to destination: %v", err)), nil
			}

			return mcp.NewToolResultText("Transformation connected to destination successfully"), nil
		},
	})

	// Tool: list_transformations
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"list_transformations",
			mcp.WithDescription("List all transformations in the workspace"),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			transformations, err := w.Workspace.ListTransformations(ctx)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to list transformations: %v", err)), nil
			}

			// If no transformations, return a simple message
			if len(transformations) == 0 {
				return mcp.NewToolResultText("No transformations found in the workspace"), nil
			}

			// Format transformations for display
			type TransformationInfo struct {
				ID           string   `json:"id"`
				Name         string   `json:"name"`
				Description  string   `json:"description"`
				Language     string   `json:"language"`
				UpdatedAt    string   `json:"updatedAt"`
				Destinations []string `json:"destinations,omitempty"`
			}

			transformationInfos := make([]TransformationInfo, 0, len(transformations))
			for _, t := range transformations {
				transformationInfos = append(transformationInfos, TransformationInfo{
					ID:           t.ID,
					Name:         t.Name,
					Description:  t.Description,
					Language:     t.Language,
					UpdatedAt:    t.UpdatedAt,
					Destinations: t.DestinationIDs,
				})
			}

			data, err := json.MarshalIndent(transformationInfos, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal transformations: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: get_transformation
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"get_transformation",
			mcp.WithDescription("Get details of a specific transformation by ID"),
			mcp.WithString("transformation_id",
				mcp.Required(),
				mcp.Description("ID of the transformation to retrieve"),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			transformationID, _ := req.Params.Arguments["transformation_id"].(string)
			if transformationID == "" {
				return mcp.NewToolResultError("transformation_id parameter is required"), nil
			}

			transformation, err := w.Workspace.GetTransformation(ctx, transformationID)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to get transformation: %v", err)), nil
			}

			// Format the transformation for display
			type TransformationDetailResponse struct {
				ID             string   `json:"id"`
				Name           string   `json:"name"`
				Description    string   `json:"description"`
				Code           string   `json:"code"`
				CreatedAt      string   `json:"createdAt"`
				UpdatedAt      string   `json:"updatedAt"`
				VersionID      string   `json:"versionId"`
				CodeVersion    string   `json:"codeVersion"`
				Language       string   `json:"language"`
				DestinationIDs []string `json:"destinationIds,omitempty"`
			}

			response := TransformationDetailResponse{
				ID:             transformation.ID,
				Name:           transformation.Name,
				Description:    transformation.Description,
				Code:           transformation.Code,
				CreatedAt:      transformation.CreatedAt,
				UpdatedAt:      transformation.UpdatedAt,
				VersionID:      transformation.VersionID,
				CodeVersion:    transformation.CodeVersion,
				Language:       transformation.Language,
				DestinationIDs: transformation.DestinationIDs,
			}

			data, err := json.MarshalIndent(response, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal transformation: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: source_live_events
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"source_live_events",
			mcp.WithDescription("Fetch live events from a specific source"),
			mcp.WithString("source_id",
				mcp.Required(),
				mcp.Description("ID of the source to fetch events from, you can get the source ID from the `list_sources` tool"),
			),
			mcp.WithNumber("wait_for_events",
				mcp.Description("Number of events to wait for before returning (default: 0, which means return immediately with available events)"),
				mcp.DefaultNumber(0),
			),
			mcp.WithNumber("max_wait_seconds",
				mcp.Description("Maximum number of seconds to wait for events (default: 30)"),
				mcp.DefaultNumber(30),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			sourceID, _ := req.Params.Arguments["source_id"].(string)
			if sourceID == "" {
				return mcp.NewToolResultError("source_id parameter is required"), nil
			}

			from := 0

			waitForEventsNum, _ := req.Params.Arguments["wait_for_events"].(float64)
			waitForEvents := int(waitForEventsNum)

			maxWaitSeconds, _ := req.Params.Arguments["max_wait_seconds"].(float64)
			maxWait := int(maxWaitSeconds)
			if maxWait <= 0 {
				maxWait = 30 // Default to 30 seconds if invalid value
			}

			// Get all sources to find the one with the specified ID
			sources, err := w.Workspace.ListSources()
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to list sources: %v", err)), nil
			}

			var targetSource api.Source
			for _, s := range sources {
				if s.ID == sourceID {
					targetSource = s
					break
				}
			}

			if targetSource.ID == "" {
				return mcp.NewToolResultError(fmt.Sprintf("Source with ID %s not found", sourceID)), nil
			}

			// Create a collection of events
			var allEvents []api.Event
			var lastEventID int64 = int64(from)

			// If wait_for_events is specified, keep fetching until we have enough events or timeout
			startTime := time.Now()
			maxDuration := time.Duration(maxWait) * time.Second

			for {
				// Fetch live events from the source
				events, err := w.Workspace.SourceLiveEvents(targetSource, lastEventID)
				if err != nil {
					return mcp.NewToolResultError(fmt.Sprintf("Failed to fetch live events: %v", err)), nil
				}

				// Add events to our collection
				allEvents = append(allEvents, events...)

				// Update lastEventID for next fetch if we have events
				if len(events) > 0 {
					lastEventID = events[len(events)-1].ID + 1
				}

				// Check if we have enough events or should stop waiting
				if waitForEvents <= 0 || len(allEvents) >= waitForEvents {
					break
				}

				// Check if we've exceeded the maximum wait time
				if time.Since(startTime) > maxDuration {
					break
				}

				// Sleep briefly before trying again
				time.Sleep(time.Second)
			}

			if len(allEvents) == 0 {
				return mcp.NewToolResultText("No live events found for the source"), nil
			}

			// Include wait information in the response
			type EventsResponse struct {
				Events         []api.Event `json:"events"`
				RequestedCount int         `json:"requestedCount"`
				ActualCount    int         `json:"actualCount"`
				WaitedSeconds  float64     `json:"waitedSeconds"`
				ReachedTimeout bool        `json:"reachedTimeout"`
			}

			response := EventsResponse{
				Events:         allEvents,
				RequestedCount: waitForEvents,
				ActualCount:    len(allEvents),
				WaitedSeconds:  time.Since(startTime).Seconds(),
				ReachedTimeout: time.Since(startTime) >= maxDuration,
			}

			data, err := json.MarshalIndent(response, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal events: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: destination_live_events
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"destination_live_events",
			mcp.WithDescription("Fetch live events from a specific destination"),
			mcp.WithString("destination_id",
				mcp.Required(),
				mcp.Description("ID of the destination to fetch events from, you can get the destination ID from the `list_destinations` tool"),
			),
			mcp.WithNumber("wait_for_events",
				mcp.Description("Number of events to wait for before returning (default: 0, which means return immediately with available events)"),
				mcp.DefaultNumber(2),
			),
			mcp.WithNumber("max_wait_seconds",
				mcp.Description("Maximum number of seconds to wait for events (default: 30)"),
				mcp.DefaultNumber(30),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			destinationID, _ := req.Params.Arguments["destination_id"].(string)
			if destinationID == "" {
				return mcp.NewToolResultError("destination_id parameter is required"), nil
			}

			fromID, _ := req.Params.Arguments["from"].(float64)
			from := int64(fromID)

			waitForEventsNum, _ := req.Params.Arguments["wait_for_events"].(float64)
			waitForEvents := int(waitForEventsNum)
			if waitForEvents <= 0 {
				waitForEvents = 2
			}

			maxWaitSeconds, _ := req.Params.Arguments["max_wait_seconds"].(float64)
			maxWait := int(maxWaitSeconds)
			if maxWait <= 0 {
				maxWait = 30 // Default to 30 seconds if invalid value
			}

			// Get all destinations to find the one with the specified ID
			destinations, err := w.Workspace.ListDestinations()
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to list destinations: %v", err)), nil
			}

			var targetDestination api.Destination
			for _, d := range destinations {
				if d.ID == destinationID {
					targetDestination = d
					break
				}
			}

			if targetDestination.ID == "" {
				return mcp.NewToolResultError(fmt.Sprintf("Destination with ID %s not found", destinationID)), nil
			}

			// Create a collection of events
			var allEvents []api.DestinationLiveEvent
			lastEventID := from

			// If wait_for_events is specified, keep fetching until we have enough events or timeout
			startTime := time.Now()
			maxDuration := time.Duration(maxWait) * time.Second

			for {
				// Fetch live events from the destination
				events, err := w.Workspace.DestinationLiveEvents(targetDestination, lastEventID)
				if err != nil {
					return mcp.NewToolResultError(fmt.Sprintf("Failed to fetch live events: %v", err)), nil
				}

				// Add events to our collection
				allEvents = append(allEvents, events...)

				// Update lastEventID for next fetch if we have events
				if len(events) > 0 {
					lastEventID = events[len(events)-1].ID + 1
				}

				// Check if we have enough events or should stop waiting
				if waitForEvents <= 0 || len(allEvents) >= waitForEvents {
					break
				}

				// Check if we've exceeded the maximum wait time
				if time.Since(startTime) > maxDuration {
					break
				}

				// Sleep briefly before trying again
				time.Sleep(time.Second)
			}

			if len(allEvents) == 0 {
				return mcp.NewToolResultText("No live events found for the destination"), nil
			}

			// Include wait information in the response
			type EventsResponse struct {
				Events         []api.DestinationLiveEvent `json:"events"`
				RequestedCount int                        `json:"requestedCount"`
				ActualCount    int                        `json:"actualCount"`
				WaitedSeconds  float64                    `json:"waitedSeconds"`
				ReachedTimeout bool                       `json:"reachedTimeout"`
			}

			response := EventsResponse{
				Events:         allEvents,
				RequestedCount: waitForEvents,
				ActualCount:    len(allEvents),
				WaitedSeconds:  time.Since(startTime).Seconds(),
				ReachedTimeout: time.Since(startTime) >= maxDuration,
			}

			data, err := json.MarshalIndent(response, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal events: %v", err)), nil
			}
			return mcp.NewToolResultText(string(data)), nil
		},
	})

	// Tool: audit_logs
	tools = append(tools, server.ServerTool{
		Tool: mcp.NewTool(
			"audit_logs",
			mcp.WithDescription("Get audit logs for the current workspace with pagination support"),
			mcp.WithNumber("skip",
				mcp.Description("Number of entries to skip (for pagination)"),
				mcp.DefaultNumber(0),
			),
			mcp.WithNumber("limit",
				mcp.Description("Maximum number of entries to return"),
				mcp.DefaultNumber(20),
			),
			mcp.WithString("target_type",
				mcp.Description("Filter logs by target type"),
				mcp.Enum(
					"source",
					"destination",
					"transformation",
					"profiles",
					"credential",
					"user",
					"user_invitation",
					"service_access_token",
				),
			),
		),
		Handler: func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
			skip := int(getParamAsFloat64(req.Params.Arguments, "skip", 0))
			limit := int(getParamAsFloat64(req.Params.Arguments, "limit", 20))
			targetType, _ := req.Params.Arguments["target_type"].(string)

			logs, err := w.Workspace.GetAuditLogs(ctx, skip, limit, targetType)
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to fetch audit logs: %v", err)), nil
			}

			// Process each audit log entry
			mcplogs := make([]MCPAuditLogEntry, 0, len(logs))
			for _, log := range logs {
				mcplog, err := processAuditLog(log)
				if err != nil {
					return mcp.NewToolResultError(fmt.Sprintf("Failed to process audit log: %v", err)), nil
				}
				mcplogs = append(mcplogs, *mcplog)
			}

			data, err := json.MarshalIndent(mcplogs, "", "  ")
			if err != nil {
				return mcp.NewToolResultError(fmt.Sprintf("Failed to marshal audit logs: %v", err)), nil
			}

			return mcp.NewToolResultText(string(data)), nil
		},
	})

	return tools
}

// Helper function to get float64 parameter with default value
func getParamAsFloat64(params map[string]interface{}, key string, defaultValue float64) float64 {
	if val, ok := params[key].(float64); ok {
		return val
	}
	return defaultValue
}
