package install

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
)

// ClaudeDesktopClient implements Client for Claude Desktop
type ClaudeDesktopClient struct{}

var (
	ClaudeDesktop Client = &ClaudeDesktopClient{}
)

// Install installs the MCP server to Claude Desktop
func (c *ClaudeDesktopClient) Install(config MCPConfig) error {
	// Get config path
	configDir, err := c.getClaudeDesktopConfigDir()
	if err != nil {
		return err
	}

	configPath, err := getConfigPath(configDir, "claude_desktop_config.json")
	if err != nil {
		return err
	}

	// Create config object
	mcpConfig := struct {
		Command string            `json:"command"`
		Args    []string          `json:"args,omitempty"`
		Envs    map[string]string `json:"envs,omitempty"`
	}{
		Command: config.BinPath,
		Args:    config.Args,
		Envs:    config.Envs,
	}

	mcpConfigJSON, err := json.Marshal(mcpConfig)
	if err != nil {
		return fmt.Errorf("failed to marshal MCP config: %w", err)
	}

	// Read existing config
	servers, err := readConfig(configPath)
	if err != nil {
		return err
	}

	// Update config
	servers["rudder"] = json.RawMessage(mcpConfigJSON)

	// Write updated config
	return writeConfig(configPath, servers)
}

// Uninstall removes the MCP server from Claude Desktop
func (c *ClaudeDesktopClient) Uninstall() error {
	// Get config path
	configDir, err := c.getClaudeDesktopConfigDir()
	if err != nil {
		return err
	}

	configPath, err := getConfigPath(configDir, "claude_desktop_config.json")
	if err != nil {
		return err
	}

	// Check if config file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// Config file doesn't exist, nothing to uninstall
		return nil
	}

	// Read existing config
	servers, err := readConfig(configPath)
	if err != nil {
		return err
	}

	// Remove server entry
	delete(servers, "rudder")

	// Write updated config
	return writeConfig(configPath, servers)
}

// getClaudeDesktopConfigDir returns the configuration directory for Claude Desktop
func (c *ClaudeDesktopClient) getClaudeDesktopConfigDir() (string, error) {
	switch runtime.GOOS {
	case "darwin":
		return filepath.Join(os.Getenv("HOME"), "Library", "Application Support", "Claude"), nil
	case "windows":
		return filepath.Join(os.Getenv("APPDATA"), "Claude"), nil
	case "linux":
		return filepath.Join(os.Getenv("HOME"), ".config", "Claude"), nil
	default:
		return "", fmt.Errorf("unsupported platform: %s", runtime.GOOS)
	}
}

// ConfigFile returns the path to the configuration file for Claude Desktop
func (c *ClaudeDesktopClient) ConfigFile() (string, error) {
	configDir, err := c.getClaudeDesktopConfigDir()
	if err != nil {
		return "", err
	}

	return getConfigPath(configDir, "claude_desktop_config.json")
}
