package install

import (
	"encoding/json"
	"fmt"
	"os"
	"path"
)

// MCPConfig represents the common configuration for an MCP server
type MCPConfig struct {
	BinPath string
	Args    []string
	Envs    map[string]string
}

// Client defines the interface for client-specific installation
type Client interface {
	// Install installs the MCP server to the client with the given configuration
	Install(config MCPConfig) error
	// Uninstall removes the MCP server from the client
	Uninstall() error
	// ConfigFile returns the path to the configuration file
	ConfigFile() (string, error)
}

// getConfigPath returns the path to the configuration file
func getConfigPath(configDir, fileName string) (string, error) {
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create directory %s: %w", configDir, err)
	}
	return path.Join(configDir, fileName), nil
}

// readConfig reads the existing configuration file or returns an empty config
func readConfig(configPath string) (map[string]json.RawMessage, error) {
	var config struct {
		MCPServers map[string]json.RawMessage `json:"mcpServers"`
	}

	// Read existing config if present
	existingData, err := os.ReadFile(configPath)
	if err != nil && !os.IsNotExist(err) {
		return nil, fmt.Errorf("failed to read existing config: %w", err)
	}

	if len(existingData) == 0 {
		existingData = []byte("{}")
	}

	if err := json.Unmarshal(existingData, &config); err != nil {
		return nil, err
	}

	if config.MCPServers == nil {
		config.MCPServers = make(map[string]json.RawMessage)
	}

	return config.MCPServers, nil
}

// writeConfig writes the updated configuration file
func writeConfig(configPath string, servers map[string]json.RawMessage) error {
	config := struct {
		MCPServers map[string]json.RawMessage `json:"mcpServers"`
	}{
		MCPServers: servers,
	}

	updatedData, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(configPath, updatedData, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}
