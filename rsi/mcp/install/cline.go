package install

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
)

var (
	Cline Client = &ClineClient{}
)

type ClineClient struct{}

// Install installs the MCP server to Cline
func (c *ClineClient) Install(config MCPConfig) error {
	// Get config path
	configDir, err := getClineConfigDir()
	if err != nil {
		return err
	}

	configPath, err := getConfigPath(configDir, "cline_mcp_settings.json")
	if err != nil {
		return err
	}

	// Create config object
	mcpConfig := struct {
		Command string            `json:"command"`
		Args    []string          `json:"args,omitempty"`
		Env     map[string]string `json:"env,omitempty"`
	}{
		Command: config.BinPath,
		Args:    config.Args,
		Env:     config.Envs,
	}

	mcpConfigJSON, err := json.Marshal(mcpConfig)
	if err != nil {
		return fmt.Errorf("failed to marshal MCP config: %w", err)
	}

	// Read existing config
	servers, err := readConfig(configPath)
	if err != nil {
		return err
	}

	// Update config
	servers["rudder"] = json.RawMessage(mcpConfigJSON)

	// Write updated config
	return writeConfig(configPath, servers)
}

// Uninstall removes the MCP server from Cline
func (c *ClineClient) Uninstall() error {
	// Get config path
	configDir, err := getClineConfigDir()
	if err != nil {
		return err
	}

	configPath, err := getConfigPath(configDir, "cline_mcp_settings.json")
	if err != nil {
		return err
	}

	// Check if config file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// Config file doesn't exist, nothing to uninstall
		return nil
	}

	// Read existing config
	servers, err := readConfig(configPath)
	if err != nil {
		return err
	}

	// Remove server entry
	delete(servers, "rudder")

	// Write updated config
	return writeConfig(configPath, servers)
}

// getClineConfigDir returns the configuration directory for Cline
func getClineConfigDir() (string, error) {
	switch runtime.GOOS {
	case "darwin":
		return filepath.Join(os.Getenv("HOME"), "Library", "Application Support", "Code", "User", "globalStorage", "saoudrizwan.claude-dev", "settings"), nil
	case "windows":
		return filepath.Join(os.Getenv("APPDATA"), "Code", "User", "globalStorage", "saoudrizwan.claude-dev", "settings"), nil
	case "linux":
		return filepath.Join(os.Getenv("HOME"), ".config", "Code", "User", "globalStorage", "saoudrizwan.claude-dev", "settings"), nil
	default:
		return "", fmt.Errorf("unsupported platform: %s", runtime.GOOS)
	}
}

// ConfigFile returns the path to the configuration file for Cline
func (c *ClineClient) ConfigFile() (string, error) {
	configDir, err := getClineConfigDir()
	if err != nil {
		return "", err
	}

	return getConfigPath(configDir, "cline_mcp_settings.json")
}
