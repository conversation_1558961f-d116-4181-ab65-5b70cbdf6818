package install

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
)

var (
	Cursor Client = &CursorClient{}
)

type CursorClient struct{}

// Install installs the MCP server to Cursor
func (c *CursorClient) Install(config MCPConfig) error {
	// Get config path
	configDir, err := getCursorConfigDir()
	if err != nil {
		return err
	}

	configPath, err := getConfigPath(configDir, "mcp.json")
	if err != nil {
		return err
	}

	// Create config object
	mcpConfig := struct {
		Command string            `json:"command"`
		Args    []string          `json:"args,omitempty"`
		Envs    map[string]string `json:"envs,omitempty"`
	}{
		Command: config.BinPath,
		Args:    config.Args,
		Envs:    config.Envs,
	}

	mcpConfigJSON, err := json.Marshal(mcpConfig)
	if err != nil {
		return fmt.Errorf("failed to marshal MCP config: %w", err)
	}

	// Read existing config
	servers, err := readConfig(configPath)
	if err != nil {
		return err
	}

	// Update config
	servers["rudder"] = json.RawMessage(mcpConfigJSON)

	// Write updated config
	return writeConfig(configPath, servers)
}

// Uninstall removes the MCP server from Cursor
func (c *CursorClient) Uninstall() error {
	// Get config path
	configDir, err := getCursorConfigDir()
	if err != nil {
		return err
	}

	configPath, err := getConfigPath(configDir, "mcp.json")
	if err != nil {
		return err
	}

	// Check if config file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// Config file doesn't exist, nothing to uninstall
		return nil
	}

	// Read existing config
	servers, err := readConfig(configPath)
	if err != nil {
		return err
	}

	// Remove server entry
	delete(servers, "rudder")

	// Write updated config
	return writeConfig(configPath, servers)
}

// getCursorConfigDir returns the configuration directory for Cursor
func getCursorConfigDir() (string, error) {
	switch runtime.GOOS {
	case "darwin":
		return filepath.Join(os.Getenv("HOME"), ".cursor"), nil
	case "windows":
		return filepath.Join(os.Getenv("USERPROFILE"), ".cursor"), nil
	case "linux":
		return filepath.Join(os.Getenv("HOME"), ".cursor"), nil
	default:
		return "", fmt.Errorf("unsupported platform: %s", runtime.GOOS)
	}
}

// ConfigFile returns the path to the configuration file for Cursor
func (c *CursorClient) ConfigFile() (string, error) {
	configDir, err := getCursorConfigDir()
	if err != nil {
		return "", err
	}

	return getConfigPath(configDir, "mcp.json")
}
