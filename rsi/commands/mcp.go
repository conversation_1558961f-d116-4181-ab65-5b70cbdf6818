package commands

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/urfave/cli/v2"

	"github.com/rudderlabs/rs-ai/rsi/mcp"
	"github.com/rudderlabs/rs-ai/rsi/mcp/install"
	"github.com/rudderlabs/rs-ai/rsi/profile"
)

var MCP = &cli.Command{
	Name:  "mcp-server",
	Usage: "Expose MCP server functionality via CLI",
	Flags: []cli.Flag{
		&cli.StringFlag{
			Name:  "profile",
			Usage: "Profile to use from the config file",
			Value: profile.Default,
		},
	},
	Subcommands: []*cli.Command{
		{
			Name:  "install",
			Usage: "Install MCP server integration",
			Flags: []cli.Flag{
				&cli.BoolFlag{
					Name:  "claude",
					Usage: "Install for Claude integration",
				},
				&cli.BoolFlag{
					Name:  "cline",
					Usage: "Install for Cline integration",
				},
				&cli.Bool<PERSON>lag{
					Name:  "cursor",
					Usage: "Install for Cursor integration",
				},
				&cli.BoolFlag{
					Name:  "all",
					Usage: "Install for all supported clients",
				},
			},
			Action: func(c *cli.Context) error {
				// Create a new installer with the executable path
				binaryPath, err := os.Executable()
				if err != nil {
					return fmt.Errorf("failed to detect binary path: %w", err)
				}

				config := install.MCPConfig{
					BinPath: binaryPath,
					Args:    []string{"mcp-server"},
					Envs: map[string]string{
						"HOME": os.Getenv("HOME"),
					},
				}

				// Check if at least one target is specified
				if !c.Bool("claude") && !c.Bool("cline") && !c.Bool("cursor") && !c.Bool("all") {
					fmt.Println("add the following to your mcp config file:")
					jsonConfig, err := json.MarshalIndent(config, "", "  ")
					if err != nil {
						return fmt.Errorf("failed to marshal config: %w", err)
					}
					fmt.Println(string(jsonConfig))
					fmt.Println("or use the --claude, --cline, --cursor, or --all flag to install for a specific client")
					return nil
				}

				// Install for selected clients
				if c.Bool("claude") || c.Bool("all") {
					if err := install.ClaudeDesktop.Install(config); err != nil {
						return fmt.Errorf("failed to install MCP server for Claude Desktop: %w", err)
					}
					configPath, err := install.ClaudeDesktop.ConfigFile()
					if err != nil {
						fmt.Println("MCP server successfully installed for Claude Desktop")
					} else {
						fmt.Printf("MCP server successfully installed for Claude Desktop at: %q\n", configPath)
					}
				}

				if c.Bool("cline") || c.Bool("all") {
					if err := install.Cline.Install(config); err != nil {
						return fmt.Errorf("failed to install MCP server for Cline: %w", err)
					}
					configPath, err := install.Cline.ConfigFile()
					if err != nil {
						fmt.Println("MCP server successfully installed for Cline")
					} else {
						fmt.Printf("MCP server successfully installed for Cline at: %q\n", configPath)
					}
				}

				if c.Bool("cursor") || c.Bool("all") {
					if err := install.Cursor.Install(config); err != nil {
						return fmt.Errorf("failed to install MCP server for Cursor: %w", err)
					}
					configPath, err := install.Cursor.ConfigFile()
					if err != nil {
						fmt.Println("MCP server successfully installed for Cursor")
					} else {
						fmt.Printf("MCP server successfully installed for Cursor at: %q\n", configPath)
					}
				}

				return nil
			},
		},
		{
			Name:  "uninstall",
			Usage: "Uninstall MCP server integration",
			Flags: []cli.Flag{
				&cli.BoolFlag{
					Name:  "claude",
					Usage: "Uninstall from Claude integration",
				},
				&cli.BoolFlag{
					Name:  "cline",
					Usage: "Uninstall from Cline integration",
				},
				&cli.BoolFlag{
					Name:  "cursor",
					Usage: "Uninstall from Cursor integration",
				},
				&cli.BoolFlag{
					Name:  "all",
					Usage: "Uninstall from all supported clients",
				},
			},
			Action: func(c *cli.Context) error {
				// Check if at least one target is specified
				if !c.Bool("claude") && !c.Bool("cline") && !c.Bool("cursor") && !c.Bool("all") {
					return fmt.Errorf("no uninstallation target specified; use --claude, --cline, --cursor, or --all")
				}

				// Uninstall from selected clients
				if c.Bool("claude") || c.Bool("all") {
					if err := install.ClaudeDesktop.Uninstall(); err != nil {
						return fmt.Errorf("failed to uninstall MCP server from Claude Desktop: %w", err)
					}
					fmt.Println("MCP server successfully uninstalled from Claude Desktop")
				}

				if c.Bool("cline") || c.Bool("all") {
					if err := install.Cline.Uninstall(); err != nil {
						return fmt.Errorf("failed to uninstall MCP server from Cline: %w", err)
					}
					fmt.Println("MCP server successfully uninstalled from Cline")
				}

				if c.Bool("cursor") || c.Bool("all") {
					if err := install.Cursor.Uninstall(); err != nil {
						return fmt.Errorf("failed to uninstall MCP server from Cursor: %w", err)
					}
					fmt.Println("MCP server successfully uninstalled from Cursor")
				}

				return nil
			},
		},
	},
	Action: func(c *cli.Context) error {
		profileName := c.String("profile")
		pm := profile.NewProfileManager()
		prof, err := pm.GetProfile(profileName)
		if err != nil {
			return fmt.Errorf("failed to load profile: %w", err)
		}
		if prof.Token == "" || prof.WorkspaceID == "" {
			return fmt.Errorf("profile is missing token or workspace-id")
		}

		mcpServer := mcp.NewMCPServer(prof)
		return mcpServer.Run()

	},
}
