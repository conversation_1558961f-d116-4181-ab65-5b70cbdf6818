package commands

import (
	"fmt"
	"strings"

	"github.com/rudderlabs/rs-ai/rsi/api"
	"github.com/urfave/cli/v2"
)

var Connections = &cli.Command{
	Name:  "connections",
	Usage: "connection related commands",
	Subcommands: []*cli.Command{
		{
			Name:  "list",
			Usage: "List all connections between sources and destinations",
			Action: func(c *cli.Context) error {
				auth := api.PATAuth(c.String("token"))
				client := api.NewClient(auth).ForWorkspace(c.String("workspace-id"))
				connections, err := client.ListConnections()
				if err != nil {
					return fmt.Errorf("error listing connections: %w", err)
				}

				fmt.Println("Connections:")
				fmt.Printf("%-28s %-28s %-28s %-8s %-8s %-24s %-24s\n", "ID", "SourceID", "DestinationID", "Enabled", "Deleted", "CreatedAt", "UpdatedAt")
				fmt.Println(strings.Repeat("-", 148))
				for _, conn := range connections {
					fmt.Printf("%-28s %-28s %-28s %-8t %-8t %-24s %-24s\n",
						conn.ID, conn.SourceID, conn.DestinationID, conn.Enabled, conn.Deleted, conn.CreatedAt.Format("2006-01-02 15:04:05"), conn.UpdatedAt.Format("2006-01-02 15:04:05"))
				}
				return nil
			},
		},
	},
}
