package commands

import (
	"context"
	"embed"
	"fmt"
	"strings"
	"time"

	"github.com/fatih/color"
	"github.com/manifoldco/promptui"
	"github.com/rudderlabs/rs-ai/rsi/api"
	"github.com/urfave/cli/v2"
)

//go:embed payloads/*
var payloads embed.FS

var Sources = &cli.Command{

	Name:  "sources",
	Usage: "source related commands",
	Subcommands: []*cli.Command{
		{
			Name:  "list",
			Usage: "List all sources",
			Flags: []cli.Flag{},
			Action: func(c *cli.Context) error {
				auth := api.PATAuth(c.String("token"))
				client := api.NewClient(auth).ForWorkspace(c.String("workspace-id"))
				return listSources(client)
			},
		},
		{
			Name:  "live",
			Usage: "Fetch live events using a write key",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name: "write-key",
				},
			},
			Action: func(c *cli.Context) error {
				auth := api.PATAuth(c.String("token"))
				client := api.NewClient(auth).ForWorkspace(c.String("workspace-id"))
				writeKey := c.String("write-key")

				if writeKey != "" {
					source, err := getSourceByWriteKey(client, writeKey)
					if err != nil {
						return err
					}
					return fetchLiveEvents(c.Context, client, *source)
				}

				if writeKey == "" {
					selectedSource, err := interactiveSourceSelection(client)
					if err != nil {
						return err
					}
					return fetchLiveEvents(c.Context, client, *selectedSource)
				}

				return nil
			},
		},
		{
			Name:  "send",
			Usage: "Send event to rudder",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name: "write-key",
				},
				&cli.StringFlag{
					Name:  "duration",
					Usage: "Duration to send events (e.g., '2m' for two minutes)",
				},
				&cli.BoolFlag{
					Name:  "forever",
					Usage: "Send events indefinitely",
				},
				&cli.IntFlag{
					Name:  "count",
					Usage: "Number of requests to send per second",
					Value: 1,
				},
			},
			Action: func(c *cli.Context) error {
				auth := api.PATAuth(c.String("token"))
				client := api.NewClient(auth).ForWorkspace(c.String("workspace-id"))
				writeKey := c.String("write-key")
				duration := c.String("duration")
				forever := c.Bool("forever")
				count := c.Int("count")

				if writeKey == "" {
					selectedSource, err := interactiveSourceSelection(client)
					if err != nil {
						return err
					}
					writeKey = selectedSource.WriteKey
				}

				return sendEvent(c.Context, client, writeKey, duration, forever, count)
			},
		},
	},
}

func listSources(client *api.Workspace) error {
	sources, err := client.ListSources()
	if err != nil {
		return fmt.Errorf("error listing sources: %w", err)
	}

	fmt.Println("Sources:")
	fmt.Printf("%-30s %-30s %-36s %-40s\n", " Name", "Type", "ID", "Write Key") // Header for the table
	fmt.Println(strings.Repeat("-", 130))                                       // Updated separator line length
	for _, source := range sources {
		fmt.Printf("%-30s %-30s %-36s %-40s\n", source.Name, source.SourceDefinition.DisplayName, source.ID, source.WriteKey) // Formatted output
	}

	return nil
}

func fetchLiveEvents(ctx context.Context, client *api.Workspace, source api.Source) error {
	from := int64(0)

	// Create a ticker for periodic fetching
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			// Stop if the context is cancelled
			return ctx.Err()
		case <-ticker.C:
			liveEvents, err := client.SourceLiveEvents(source, from)
			if err != nil {
				return fmt.Errorf("error fetching live events: %w", err)
			}

			for _, event := range liveEvents {
				// Create color functions
				cyan := color.New(color.FgCyan).SprintFunc()
				green := color.New(color.FgGreen).SprintFunc()
				yellow := color.New(color.FgYellow).SprintFunc()
				red := color.New(color.FgRed).SprintFunc()
				blue := color.New(color.FgBlue).SprintFunc()
				// Format the output
				fmt.Printf("\n%s\n", cyan(strings.Repeat("-", 16)))
				fmt.Printf("%-9s %s\n", "Time:", truncate(green(event.Event.ReceivedAt), 100))
				fmt.Printf("%-9s %s\n", "Type:", truncate(yellow(event.EventType), 100))
				fmt.Printf("%-9s %s\n", "Name:", truncate(blue(event.EventName), 100))
				fmt.Printf("%-9s %s\n", "AnonymID:", truncate(event.Event.AnonymousID, 100))
				if event.ErrorCode == 200 {
					fmt.Printf("%-9s %s\n", "Status:", green("Ingested"))
				} else if event.ErrorCode != 0 {
					fmt.Printf("%-9s %s\n", "Error:", red(fmt.Sprintf("Code=%d", event.ErrorCode)))
				}

				// Update 'from' to the largest event ID
				if event.ID > from {
					from = event.ID
				}
			}
		}
	}
}

// truncate shortens a string to the specified length if it's longer
func truncate(s string, length int) string {
	if len(s) <= length {
		return s
	}
	return s[:length-3] + "..."
}

func sendEvent(ctx context.Context, client *api.Workspace, writeKey, duration string, forever bool, count int) error {
	workspaceSettings, err := client.WorkspaceSettings()
	if err != nil {
		return fmt.Errorf("error fetching workspace settings: %w", err)
	}

	fmt.Println("Sending to data plane URL:", workspaceSettings.DataPlaneURL)

	dataPlaneURL := workspaceSettings.DataPlaneURL

	dataplaneClient := api.NewDataplaneClient(dataPlaneURL, writeKey)

	// Send the first batch of events immediately
	for i := 0; i < count; i++ {
		if err := dataplaneClient.SendEvent(ctx); err != nil {
			return err
		}
	}

	// If no duration is specified and not running forever, exit after sending one batch
	if duration == "" && !forever {
		return nil
	}

	var endTime time.Time
	if duration != "" {
		d, err := time.ParseDuration(duration)
		if err != nil {
			return fmt.Errorf("invalid duration format: %w", err)
		}
		endTime = time.Now().Add(d)
	}

	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			for i := 0; i < count; i++ {
				if err := dataplaneClient.SendEvent(ctx); err != nil {
					return err
				}
			}

			if !forever && duration != "" && time.Now().After(endTime) {
				return nil
			}
		}
	}
}

func interactiveSourceSelection(client *api.Workspace) (*api.Source, error) {
	sources, err := client.ListSources()
	if err != nil {
		return nil, fmt.Errorf("error fetching sources: %w", err)
	}

	templates := &promptui.SelectTemplates{
		Label:    "{{ . }}",
		Active:   "> {{ .Name | cyan }} ({{ .SourceDefinition.DisplayName | red }})",
		Inactive: "  {{ .Name | cyan }} ({{ .SourceDefinition.DisplayName | red }})",
		Selected: "> {{ .Name | red | cyan }}",
		Details: `
--------- Source ----------
{{ "Name:" | faint }}	{{ .Name }}
{{ "Type:" | faint }}	{{ .SourceDefinition.DisplayName }}
{{ "ID:" | faint }}	{{ .ID }}
{{ "Write Key:" | faint }}	{{ .WriteKey }}`,
	}

	prompt := promptui.Select{
		Label:     "Select a source",
		Items:     sources,
		Templates: templates,
		Size:      10,
	}

	i, _, err := prompt.Run()
	if err != nil {
		return nil, fmt.Errorf("prompt failed: %w", err)
	}

	return &sources[i], nil
}

func getSourceByWriteKey(client *api.Workspace, writeKey string) (*api.Source, error) {
	sources, err := client.ListSources()
	if err != nil {
		return nil, fmt.Errorf("error fetching sources: %w", err)
	}

	for _, s := range sources {
		if s.WriteKey == writeKey {
			return &s, nil
		}
	}

	return nil, fmt.Errorf("no source found with write key: %s", writeKey)
}
