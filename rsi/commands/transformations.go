package commands

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"github.com/manifoldco/promptui"
	"github.com/rudderlabs/rs-ai/rsi/api"
	"github.com/rudderlabs/rs-ai/rsi/profile"
	"github.com/urfave/cli/v2"
)

var Transformations = &cli.Command{
	Name:  "transformations",
	Usage: "Manage transformations",
	Subcommands: []*cli.Command{
		{
			Name:  "test",
			Usage: "Test a transformation using a code file and sample batch payload",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:     "file",
					Aliases:  []string{"f"},
					Usage:    "Path to the transformation code file (required)",
					Required: true,
				},
				&cli.StringFlag{
					Name:  "code-version",
					Value: "1",
					Usage: "Transformation code version (default '1')",
				},
				&cli.StringFlag{
					Name:  "language",
					Value: "javascript",
					Usage: "Transformation language (default 'javascript')",
				},
				&cli.BoolFlag{
					Name:  "upgrade",
					Usage: "Upgrade transformation (default false)",
				},
				&cli.StringFlag{
					Name:  "batch",
					Value: "commands/payloads/batch.json",
					Usage: "Path to the sample batch payload (default: commands/payloads/batch.json)",
				},
			},
			Action: func(c *cli.Context) error {
				codeFile := c.String("file")
				codeVersion := c.String("code-version")
				language := c.String("language")
				upgradeTransformation := c.Bool("upgrade")
				batchPath := c.String("batch")
				profileName := c.String("profile")

				codeBytes, err := os.ReadFile(codeFile)
				if err != nil {
					return fmt.Errorf("failed to read code file: %w", err)
				}

				batchBytes, err := os.ReadFile(batchPath)
				if err != nil {
					return fmt.Errorf("failed to read batch payload: %w", err)
				}

				input := string(batchBytes)

				pm := profile.NewProfileManager()
				prof, err := pm.GetProfile(profileName)
				if err != nil {
					return fmt.Errorf("failed to load profile: %w", err)
				}
				token := prof.Token
				workspaceID := prof.WorkspaceID
				if token == "" || workspaceID == "" {
					return fmt.Errorf("profile is missing token or workspace-id")
				}

				auth := api.PATAuth(token)
				client := api.NewClient(auth).ForWorkspace(workspaceID)
				ctx := context.Background()

				req := api.TransformationTestRequest{
					Code:                  string(codeBytes),
					CodeVersion:           codeVersion,
					Input:                 input,
					Language:              language,
					UpgradeTransformation: upgradeTransformation,
				}

				resp, err := client.TransformationTest(ctx, req)
				if err != nil {
					return fmt.Errorf("transformation test failed: %w", err)
				}

				out, _ := json.MarshalIndent(resp, "", "  ")
				fmt.Println(string(out))
				return nil
			},
		},
		{
			Name:  "new",
			Usage: "Create a new transformation file from a sample transformation",
			Action: func(c *cli.Context) error {
				profileName := c.String("profile")
				pm := profile.NewProfileManager()
				prof, err := pm.GetProfile(profileName)
				if err != nil {
					return fmt.Errorf("failed to load profile: %w", err)
				}
				auth := api.PATAuth(prof.Token)
				client := api.NewClient(auth).ForWorkspace(prof.WorkspaceID)
				ctx := context.Background()

				samples, err := client.SampleTransformations(ctx)
				if err != nil {
					return fmt.Errorf("failed to fetch sample transformations: %w", err)
				}
				if len(samples) == 0 {
					return fmt.Errorf("no sample transformations available")
				}

				promptItems := make([]string, len(samples))
				for i, s := range samples {
					promptItems[i] = fmt.Sprintf("%s: %s", s.Name, s.Description)
				}
				prompt := promptui.Select{
					Label: "Select a sample transformation as a starting point",
					Items: promptItems,
				}
				idx, _, err := prompt.Run()
				if err != nil {
					return fmt.Errorf("prompt failed: %w", err)
				}
				selected := samples[idx]

				// Create a filename from the transformation name
				filename := strings.ToLower(selected.Name)
				filename = strings.ReplaceAll(filename, " ", "_")
				filename = fmt.Sprintf("%s.js", filename)

				if _, err := os.Stat(filename); err == nil {
					return fmt.Errorf("file %s already exists", filename)
				}
				if err := os.WriteFile(filename, []byte(selected.Code), 0644); err != nil {
					return fmt.Errorf("failed to write file: %w", err)
				}
				fmt.Printf("Created new transformation file: %s\n", filename)
				return nil
			},
		},
		{
			Name:  "create",
			Usage: "Create a new transformation in RudderStack",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:  "id",
					Usage: "ID of the transformation to update (for upsert)",
				},
				&cli.StringFlag{
					Name:     "file",
					Aliases:  []string{"f"},
					Usage:    "Path to the transformation code file (required)",
					Required: true,
				},
				&cli.StringFlag{
					Name:     "name",
					Usage:    "Name of the transformation (required)",
					Required: true,
				},
				&cli.StringFlag{
					Name:  "description",
					Usage: "Description of the transformation",
				},
				&cli.StringFlag{
					Name:  "language",
					Value: "javascript",
					Usage: "Language of the transformation (default: javascript)",
				},
			},
			Action: func(c *cli.Context) error {
				profileName := c.String("profile")
				pm := profile.NewProfileManager()
				prof, err := pm.GetProfile(profileName)
				if err != nil {
					return fmt.Errorf("failed to load profile: %w", err)
				}
				auth := api.PATAuth(prof.Token)
				client := api.NewClient(auth).ForWorkspace(prof.WorkspaceID)
				ctx := context.Background()

				codeFile := c.String("file")
				codeBytes, err := os.ReadFile(codeFile)
				if err != nil {
					return fmt.Errorf("failed to read code file: %w", err)
				}

				name := c.String("name")
				description := c.String("description")
				language := c.String("language")
				id := c.String("id")

				req := api.SaveTransformationRequest{
					ID:                    id,
					Name:                  name,
					Description:           description,
					Code:                  string(codeBytes),
					UpgradeTransformation: false,
					WorkspaceID:           prof.WorkspaceID,
					Language:              language,
				}

				jsonBytes, _ := json.MarshalIndent(req, "", "  ")
				fmt.Println(string(jsonBytes))

				resp, err := client.SaveTransformation(ctx, req)
				if err != nil {
					return fmt.Errorf("failed to save transformation: %w", err)
				}

				out, _ := json.MarshalIndent(resp, "", "  ")
				fmt.Println(string(out))
				return nil
			},
		},
		{
			Name:  "connect-destination",
			Usage: "Connect a transformation to a destination",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:     "transformation-id",
					Usage:    "ID of the transformation (required)",
					Required: true,
				},
				&cli.StringFlag{
					Name:     "destination-id",
					Usage:    "ID of the destination (required)",
					Required: true,
				},
				&cli.BoolFlag{
					Name:  "enable-for-cloud-mode",
					Usage: "Enable for cloud mode",
					Value: true,
				},
				&cli.BoolFlag{
					Name:  "enable-for-device-mode",
					Usage: "Enable for device mode",
					Value: false,
				},
			},
			Action: func(c *cli.Context) error {
				profileName := c.String("profile")
				pm := profile.NewProfileManager()
				prof, err := pm.GetProfile(profileName)
				if err != nil {
					return fmt.Errorf("failed to load profile: %w", err)
				}
				auth := api.PATAuth(prof.Token)
				client := api.NewClient(auth).ForWorkspace(prof.WorkspaceID)
				ctx := context.Background()

				transformationID := c.String("transformation-id")
				destinationID := c.String("destination-id")

				err = client.ConnectTransformationToDestination(ctx, transformationID, destinationID)
				if err != nil {
					return fmt.Errorf("failed to connect transformation to destination: %w", err)
				}
				fmt.Println("Transformation connected to destination successfully.")
				return nil
			},
		},
		{
			Name:  "list",
			Usage: "List all transformations in the workspace",
			Action: func(c *cli.Context) error {
				profileName := c.String("profile")
				pm := profile.NewProfileManager()
				prof, err := pm.GetProfile(profileName)
				if err != nil {
					return fmt.Errorf("failed to load profile: %w", err)
				}
				auth := api.PATAuth(prof.Token)
				client := api.NewClient(auth).ForWorkspace(prof.WorkspaceID)
				ctx := context.Background()

				transformations, err := client.ListTransformations(ctx)
				if err != nil {
					return fmt.Errorf("failed to list transformations: %w", err)
				}

				if len(transformations) == 0 {
					fmt.Println("No transformations found")
					return nil
				}

				fmt.Println("Transformations:")
				fmt.Printf("%-32s %-30s %-40s %-10s %-24s\n", "ID", "Name", "Description", "Language", "Updated")
				fmt.Println(strings.Repeat("-", 140))

				for _, t := range transformations {
					// Truncate description if too long
					desc := t.Description
					if len(desc) > 37 {
						desc = desc[:34] + "..."
					}

					fmt.Printf("%-32s %-30s %-40s %-10s %-24s\n",
						t.ID,
						t.Name,
						desc,
						t.Language,
						t.UpdatedAt,
					)
				}
				return nil
			},
		},
		{
			Name:  "get",
			Usage: "Get details of a specific transformation",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:     "id",
					Usage:    "ID of the transformation to retrieve (required)",
					Required: true,
				},
				&cli.BoolFlag{
					Name:  "code",
					Usage: "Include code in the output",
					Value: false,
				},
			},
			Action: func(c *cli.Context) error {
				profileName := c.String("profile")
				pm := profile.NewProfileManager()
				prof, err := pm.GetProfile(profileName)
				if err != nil {
					return fmt.Errorf("failed to load profile: %w", err)
				}
				auth := api.PATAuth(prof.Token)
				client := api.NewClient(auth).ForWorkspace(prof.WorkspaceID)
				ctx := context.Background()

				transformationID := c.String("id")
				showCode := c.Bool("code")

				transformation, err := client.GetTransformation(ctx, transformationID)
				if err != nil {
					return fmt.Errorf("failed to get transformation: %w", err)
				}

				// Basic info
				fmt.Println("=== Transformation Details ===")
				fmt.Printf("ID:             %s\n", transformation.ID)
				fmt.Printf("Name:           %s\n", transformation.Name)
				fmt.Printf("Description:    %s\n", transformation.Description)
				fmt.Printf("Language:       %s\n", transformation.Language)
				fmt.Printf("Version:        %s\n", transformation.VersionID)
				fmt.Printf("Code Version:   %s\n", transformation.CodeVersion)
				fmt.Printf("Created:        %s\n", transformation.CreatedAt)
				fmt.Printf("Updated:        %s\n", transformation.UpdatedAt)

				// Destinations
				if len(transformation.DestinationIDs) > 0 {
					fmt.Println("\nDestinations:")
					for _, destID := range transformation.DestinationIDs {
						fmt.Printf("  - %s\n", destID)
					}
				} else {
					fmt.Println("\nDestinations: none")
				}

				// Show code if requested
				if showCode {
					fmt.Println("\n=== Transformation Code ===")
					fmt.Println(transformation.Code)
				}

				return nil
			},
		},
	},
}
