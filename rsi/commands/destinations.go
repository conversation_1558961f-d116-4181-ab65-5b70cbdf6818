package commands

import (
	"context"
	"fmt"
	"strings"
	"time"

	"text/template"

	"github.com/fatih/color"
	"github.com/manifoldco/promptui"
	"github.com/rudderlabs/rs-ai/rsi/api"
	"github.com/urfave/cli/v2"
)

var Destinations = &cli.Command{
	Name:  "destinations",
	Usage: "destination related commands",
	Subcommands: []*cli.Command{
		{
			Name:  "list",
			Usage: "List all destinations",
			Action: func(c *cli.Context) error {
				auth := api.PATAuth(c.String("token"))
				client := api.NewClient(auth).ForWorkspace(c.String("workspace-id"))
				return listDestinations(client)
			},
		},
		{
			Name:  "health",
			Usage: "Get the health metrics of event streams for a given time range and region",
			Action: func(c *cli.Context) error {
				auth := api.PATAuth(c.String("token"))
				client := api.NewClient(auth).ForWorkspace(c.String("workspace-id"))

				// First get all destinations to map IDs to names
				destinations, err := client.ListDestinations()
				if err != nil {
					return fmt.Errorf("error listing destinations: %w", err)
				}

				// Create a map of destination IDs to names for quick lookup
				destNames := make(map[string]string)
				for _, dest := range destinations {
					destNames[dest.ID] = dest.Name
				}

				healthMetrics, err := client.StreamHealthMetrics(c.Context, time.Now().Add(-24*time.Hour), time.Now())
				if err != nil {
					return fmt.Errorf("error fetching event stream health: %w", err)
				}

				fmt.Println("Event Stream Health Metrics:")
				fmt.Printf("%-40s %-20s %-15s %-15s\n", "Destination Name", "ID", "Success", "Aborts")
				fmt.Println(strings.Repeat("-", 90))

				for _, metric := range healthMetrics {
					name := destNames[metric.DestinationID]
					if name == "" {
						name = "Unknown Destination"
					}
					fmt.Printf("%-40s %-20s %-15d %-15d\n",
						truncate(name, 39),
						truncate(metric.DestinationID, 19),
						metric.SuccessSum,
						metric.AbortSum,
					)
				}
				return nil
			},
		},
		{
			Name:  "live",
			Usage: "Fetch live events using a destination ID",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:  "destination-id",
					Usage: "ID of the destination",
				},
			},
			Action: func(c *cli.Context) error {
				auth := api.PATAuth(c.String("token"))
				client := api.NewClient(auth).ForWorkspace(c.String("workspace-id"))
				destinationID := c.String("destination-id")

				if destinationID != "" {
					destination, err := getDestinationByID(client, destinationID)
					if err != nil {
						return err
					}
					return fetchLiveDestinationEvents(c.Context, client, *destination)
				}

				// If destination ID is not provided, prompt for selection
				selectedDestination, err := interactiveDestinationSelection(client)
				if err != nil {
					return err
				}
				return fetchLiveDestinationEvents(c.Context, client, *selectedDestination)
			},
		},
		{
			Name:  "errors",
			Usage: "Show event stream errors for a destination",
			Flags: []cli.Flag{
				&cli.StringFlag{
					Name:  "destination-id",
					Usage: "ID of the destination",
				},
				&cli.StringFlag{
					Name:  "start",
					Usage: "Start time (default: 24h ago)",
					Value: time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
				},
			},
			Action: func(c *cli.Context) error {
				auth := api.PATAuth(c.String("token"))
				client := api.NewClient(auth).ForWorkspace(c.String("workspace-id"))

				var destination *api.Destination
				var err error

				destinationID := c.String("destination-id")
				if destinationID == "" {
					destination, err = interactiveDestinationSelectionWithHealth(client)
					if err != nil {
						return err
					}
				} else {
					destination, err = getDestinationByID(client, destinationID)
					if err != nil {
						return err
					}
				}

				// Show health stats for the selected destination
				// Fetch health metrics for the last 24h
				healthMetrics, err := client.StreamHealthMetrics(c.Context, time.Now().Add(-24*time.Hour), time.Now())
				if err != nil {
					return fmt.Errorf("error fetching event stream health: %w", err)
				}

				// Find health stats for the selected destination
				var found bool
				for _, metric := range healthMetrics {
					if metric.DestinationID == destination.ID {
						fmt.Println("\nEvent Stream Health Metrics (last 24h):")
						fmt.Printf("%-40s %-20s %-15s %-15s\n", "Destination Name", "ID", "Success", "Aborts")
						fmt.Println(strings.Repeat("-", 90))
						fmt.Printf("%-40s %-20s %-15d %-15d\n",
							truncate(destination.Name, 39),
							truncate(destination.ID, 19),
							metric.SuccessSum,
							metric.AbortSum,
						)
						found = true
						break
					}
				}
				if !found {
					fmt.Println("\nEvent Stream Health Metrics (last 24h):")
					fmt.Printf("%-40s %-20s %-15s %-15s\n", "Destination Name", "ID", "Success", "Aborts")
					fmt.Println(strings.Repeat("-", 90))
					fmt.Printf("%-40s %-20s %-15s %-15s\n",
						truncate(destination.Name, 39),
						truncate(destination.ID, 19),
						"-", "-")
				}

				startTime, err := time.Parse(time.RFC3339, c.String("start"))
				if err != nil {
					return fmt.Errorf("invalid start time format: %w", err)
				}

				errors, err := client.DestinationEventErrors(c.Context, destination.ID, startTime)
				if err != nil {
					return fmt.Errorf("error fetching event stream errors: %w", err)
				}

				if len(errors) == 0 {
					fmt.Printf("No errors found for destination %s (%s)\n", destination.Name, destination.ID)
					return nil
				}

				// Create interactive selection for errors
				templates := &promptui.SelectTemplates{
					Label:    "{{ . }}",
					Active:   "> {{ .EventName | cyan }} ({{ .EventType }}) [{{ .StatusCode }}] {{ timeFormat .LastSeen }}",
					Inactive: "  {{ .EventName }} ({{ .EventType }}) [{{ .StatusCode }}] {{ timeFormat .LastSeen }}",
					Selected: "> Selected: {{ .EventName | cyan }} {{ .EventType }} error",
					Details: `
---------------- Error Details ----------------
{{ "Event Name:" | faint }}	{{ .EventName }}
{{ "Event Type:" | faint }}	{{ .EventType }}
{{ "Status Code:" | faint }}	{{ .StatusCode }}
{{ "Count:" | faint }}	{{ .Count }}
{{ "Source ID:" | faint }}	{{ .SourceID }}
{{ "Reported By:" | faint }}	{{ .ReportedBy }}
{{ "Reported At:" | faint }}	{{ timeFormat .ReportedAt }}`,
					FuncMap: template.FuncMap{
						"timeFormat": func(ts int64) string {
							return time.Unix(ts/1000, 0).Format("2006-01-02 15:04:05")
						},
						"cyan":  color.New(color.FgCyan).SprintFunc(),
						"faint": color.New(color.Faint).SprintFunc(),
					},
				}

				prompt := promptui.Select{
					Label:     "\nSelect an error to view sample event",
					Items:     errors,
					Templates: templates,
					Size:      10,
				}

				idx, _, err := prompt.Run()
				if err != nil {
					if err == promptui.ErrInterrupt {
						return nil
					}
					return fmt.Errorf("prompt failed: %w", err)
				}

				// Fetch and display sample event for selected error
				selectedError := errors[idx]

				// Print error details
				cyan := color.New(color.FgCyan).SprintFunc()
				yellow := color.New(color.FgYellow).SprintFunc()
				green := color.New(color.FgGreen).SprintFunc()
				red := color.New(color.FgRed).SprintFunc()

				fmt.Println(cyan("\nError Details:"))
				fmt.Printf("%s: %s\n", yellow("Source ID"), selectedError.SourceID)
				fmt.Printf("%s: %s\n", yellow("Event Name"), selectedError.EventName)
				fmt.Printf("%s: %s\n", yellow("Event Type"), selectedError.EventType)
				fmt.Printf("%s: %d\n", yellow("Status Code"), selectedError.StatusCode)
				fmt.Printf("%s: %s\n", yellow("Reported By"), selectedError.ReportedBy)
				fmt.Printf("%s: %s\n", yellow("Last Seen"), selectedError.ReportedAt.Format("2006-01-02 15:04:05"))
				fmt.Printf("%s: %d\n", yellow("Count"), selectedError.Count)

				sample, err := client.GetSampleEvent(c.Context, selectedError)
				if err != nil {
					return fmt.Errorf("error fetching sample event: %w", err)
				}
				fmt.Println(cyan("\nSample Event:"))
				fmt.Println(green(sample.SampleEvent))
				fmt.Println(cyan("\nSample Response:"))
				fmt.Println(red(sample.SampleResponse))
				return nil
			},
		},
	},
}

func listDestinations(client *api.Workspace) error {
	destinations, err := client.ListDestinations()
	if err != nil {
		return fmt.Errorf("error listing destinations: %w", err)
	}

	fmt.Println("Destinations:")
	fmt.Printf("%-30s %-30s %-30s %-36s %-36s\n", "Name", "Type", "ID", "Created At", "Updated At")
	fmt.Println(strings.Repeat("-", 126))
	for _, destination := range destinations {
		fmt.Printf("%-30s %-30s %-30s %-36s %-36s\n", destination.Name, destination.Type, destination.ID, destination.CreatedAt.Format(time.RFC3339), destination.UpdatedAt.Format(time.RFC3339))
	}

	return nil
}

func fetchLiveDestinationEvents(ctx context.Context, client *api.Workspace, destination api.Destination) error {
	from := int64(0)

	// Create a ticker for periodic fetching
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			// Stop if the context is cancelled
			return ctx.Err()
		case <-ticker.C:
			liveEvents, err := client.DestinationLiveEvents(destination, from)
			if err != nil {
				return fmt.Errorf("error fetching live events: %w", err)
			}

			for _, event := range liveEvents {
				// Create color functions
				cyan := color.New(color.FgCyan).SprintFunc()
				green := color.New(color.FgGreen).SprintFunc()
				yellow := color.New(color.FgYellow).SprintFunc()
				red := color.New(color.FgRed).SprintFunc()
				blue := color.New(color.FgBlue).SprintFunc()
				// Format the output
				fmt.Printf("\n%s\n", cyan(strings.Repeat("-", 16)))
				fmt.Printf("%-12s %s\n", "Type:", truncate(yellow(event.EventType), 100))
				fmt.Printf("%-12s %s\n", "Name:", truncate(blue(event.EventName), 100))
				if event.ErrorCode == "200" {
					fmt.Printf("%-12s %s\n", "Status:", green("Ingested"))
				} else if event.ErrorCode != "0" {
					fmt.Printf("%-12s %s\n", "Error:", red(fmt.Sprintf("Code=%s", event.ErrorCode)))
				}

				// Update 'from' to the largest event ID
				if event.ID > from {
					from = event.ID
				}
			}
		}
	}
}

// interactiveDestinationSelection shows a basic destination selector (no health metrics)
func interactiveDestinationSelection(client *api.Workspace) (*api.Destination, error) {
	destinations, err := client.ListDestinations()
	if err != nil {
		return nil, fmt.Errorf("error fetching destinations: %w", err)
	}

	templates := &promptui.SelectTemplates{
		Label:    "{{ . }}",
		Active:   "> {{ .Name | cyan }} ({{ .Type | red }})",
		Inactive: "  {{ .Name | cyan }} ({{ .Type | red }})",
		Selected: "> Selected: {{ .Name | red | cyan }}",
		Details: `
--------- Destination ----------
{{ "Name:" | faint }}	{{ .Name }}
{{ "Type:" | faint }}	{{ .Type }}
{{ "ID:" | faint }}	{{ .ID }}`,
		FuncMap: template.FuncMap{
			"cyan":  color.New(color.FgCyan).SprintFunc(),
			"red":   color.New(color.FgRed).SprintFunc(),
			"faint": color.New(color.Faint).SprintFunc(),
		},
	}

	prompt := promptui.Select{
		Label:     "Select a destination",
		Items:     destinations,
		Templates: templates,
		Size:      10,
	}

	i, _, err := prompt.Run()
	if err != nil {
		return nil, fmt.Errorf("prompt failed: %w", err)
	}

	return &destinations[i], nil
}

// interactiveDestinationSelectionWithHealth shows destination selector with health metrics in details
func interactiveDestinationSelectionWithHealth(client *api.Workspace) (*api.Destination, error) {
	destinations, err := client.ListDestinations()
	if err != nil {
		return nil, fmt.Errorf("error fetching destinations: %w", err)
	}

	healthMetrics, err := client.StreamHealthMetrics(context.Background(), time.Now().Add(-24*time.Hour), time.Now())
	if err != nil {
		return nil, fmt.Errorf("error fetching event stream health: %w", err)
	}
	healthMap := make(map[string]struct{ Success, Aborts interface{} })
	for _, metric := range healthMetrics {
		healthMap[metric.DestinationID] = struct{ Success, Aborts interface{} }{metric.SuccessSum, metric.AbortSum}
	}

	templates := &promptui.SelectTemplates{
		Label:    "{{ . }}",
		Active:   "> {{ .Name | cyan }} ({{ .Type | red }})",
		Inactive: "  {{ .Name | cyan }} ({{ .Type | red }})",
		Selected: "> Selected: {{ .Name | red | cyan }}",
		Details: `
--------- Destination ----------
{{ "Name:" | faint }}	{{ .Name }}
{{ "Type:" | faint }}	{{ .Type }}
{{ "ID:" | faint }}	{{ .ID }}
{{ "Success (24h):" | faint }}	{{ healthSuccess .ID }}
{{ "Aborts (24h):" | faint }}	{{ healthAborts .ID }}`,
		FuncMap: template.FuncMap{
			"cyan":  color.New(color.FgCyan).SprintFunc(),
			"red":   color.New(color.FgRed).SprintFunc(),
			"faint": color.New(color.Faint).SprintFunc(),
			"healthSuccess": func(id string) interface{} {
				if v, ok := healthMap[id]; ok {
					return v.Success
				}
				return "-"
			},
			"healthAborts": func(id string) interface{} {
				if v, ok := healthMap[id]; ok {
					return v.Aborts
				}
				return "-"
			},
		},
	}

	prompt := promptui.Select{
		Label:     "Select a destination",
		Items:     destinations,
		Templates: templates,
		Size:      10,
	}

	i, _, err := prompt.Run()
	if err != nil {
		return nil, fmt.Errorf("prompt failed: %w", err)
	}

	return &destinations[i], nil
}

// getDestinationByID retrieves a destination by its ID
func getDestinationByID(client *api.Workspace, destinationID string) (*api.Destination, error) {
	destinations, err := client.ListDestinations()
	if err != nil {
		return nil, fmt.Errorf("error fetching destinations: %w", err)
	}

	for _, d := range destinations {
		if d.ID == destinationID {
			return &d, nil
		}
	}

	return nil, fmt.Errorf("no destination found with ID: %s", destinationID)
}
