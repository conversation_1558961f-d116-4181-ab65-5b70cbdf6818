package commands

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"text/tabwriter"

	"github.com/coreos/go-oidc/v3/oidc"
	"github.com/int128/oauth2cli"
	"github.com/pkg/browser"
	"github.com/rudderlabs/rs-ai/rsi/api"
	"github.com/rudderlabs/rs-ai/rsi/profile"
	"github.com/urfave/cli/v2"
	"golang.org/x/oauth2"
	"golang.org/x/sync/errgroup"
)

// isRudderStackEmail checks if the given email belongs to RudderStack domain
func isRudderStackEmail(email string) bool {
	return strings.HasSuffix(strings.ToLower(email), "@rudderstack.com")
}

// searchOption represents a search option with its flag and description
type searchOption struct {
	flag        string
	alias       string
	description string
	searchBy    api.SearchBy
}

// getSearchOptions returns all available search options
var searchOptions = []searchOption{
	{"email", "e", "Search workspaces by admin or user email", api.SearchByEmail},
	{"namespace", "n", "Search workspaces by namespace", api.SearchByNamespace},
	{"workspace-id", "w", "Search workspaces by workspace ID", api.SearchByWorkspaceID},
	{"org-id", "o", "Search workspaces by organization ID", api.SearchByOrgID},
	{"source-id", "s", "Search workspaces by source ID", api.SearchBySourceID},
	{"source-type", "", "Search workspaces by source type", api.SearchBySourceType},
	{"destination-id", "d", "Search workspaces by destination ID", api.SearchByDestinationID},
	{"destination-type", "", "Search workspaces by destination type", api.SearchByDestinationType},
}

func flagsFromSearchOptions() []cli.Flag {
	flags := []cli.Flag{}

	for _, opt := range searchOptions {

		f := &cli.StringFlag{
			Name:  opt.flag,
			Usage: opt.description,
		}

		if opt.alias != "" {
			f.Aliases = []string{opt.alias}
		}

		flags = append(flags, f)
	}

	return flags
}

// printSearchOptions prints all available search options in a formatted table
func printSearchOptions() {
	fmt.Println("Available search options:")
	w := tabwriter.NewWriter(os.Stdout, 0, 0, 2, ' ', 0)
	fmt.Fprintln(w, "Flag\tAlias\tDescription")
	fmt.Fprintln(w, "----\t-----\t-----------")

	for _, opt := range searchOptions {
		alias := "-" + opt.alias
		if opt.alias == "" {
			alias = "none"
		}
		fmt.Fprintf(w, "--%s\t%s\t%s\n", opt.flag, alias, opt.description)
	}
	w.Flush()
	fmt.Println("\nExample usage:")
	fmt.Println("  rsi admin search-workspaces --email <EMAIL>")
	fmt.Println("  rsi admin search-workspaces --workspace-id ws_123456")
}

var Admin = &cli.Command{
	Name:  "admin",
	Usage: "Admin commands",
	Subcommands: []*cli.Command{
		{
			Name:  "search-workspaces",
			Usage: "Search for workspaces by various criteria",
			Flags: flagsFromSearchOptions(),
			Action: func(c *cli.Context) error {
				var activeSearch string
				var searchValue string
				var searchType api.SearchBy

				for _, opt := range searchOptions {
					if value := c.String(opt.flag); value != "" {
						if activeSearch != "" {
							return fmt.Errorf("only one search criteria can be specified, found both --%s and --%s", activeSearch, opt.flag)
						}
						activeSearch = opt.flag
						searchValue = value
						searchType = opt.searchBy
					}
				}

				if activeSearch == "" {
					fmt.Printf("No search criteria specified.\n")
					printSearchOptions()
					return nil
				}

				pm := profile.NewProfileManager()
				prof, err := pm.GetProfile(profile.Default)
				if err != nil {
					return fmt.Errorf("failed to get profile: %w", err)
				}

				auth := api.NewAdminJWT(prof.AdminRefreshToken)

				var client *api.AdminClient

				email, err := auth.GetEmail(c.Context)
				if err != nil {
					return fmt.Errorf("failed to get email: %w", err)
				}

				client = api.NewAdminClient(email, auth, api.FromEnv("MCP"))

				// Search for workspaces
				workspaces, err := client.SearchWorkspaces(searchType, searchValue)
				if err != nil {
					return fmt.Errorf("failed to search workspaces: %w", err)
				}

				if len(workspaces) == 0 {
					fmt.Printf("No workspaces found for %s: %s\n", activeSearch, searchValue)
					return nil
				}

				// Display the workspaces in a table
				w := tabwriter.NewWriter(os.Stdout, 0, 0, 2, ' ', 0)
				fmt.Fprintln(w, "ID\tName\tOrganization ID\tData Plane URL\tStatus\tAdmin Email")

				for _, workspace := range workspaces {
					fmt.Fprintf(w, "%s\t%s\t%s\t%s\t%s\t%s\n",
						workspace.ID,
						workspace.Name,
						workspace.OrganizationID,
						workspace.DataPlaneURL,
						workspace.Status,
						workspace.AdminEmail,
					)
				}

				w.Flush()
				return nil
			},
		},
		{
			Name:  "auth",
			Usage: "Authenticate with admin credentials",
			Action: func(c *cli.Context) error {
				provider, err := oidc.NewProvider(c.Context, "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_mkh5sGdqx")
				if err != nil {
					return fmt.Errorf("failed to create provider: %w", err)
				}

				ready := make(chan string, 1)
				defer close(ready)
				pkceVerifier := oauth2.GenerateVerifier()
				cfg := oauth2cli.Config{
					OAuth2Config: oauth2.Config{
						ClientID:     "3pff4gmpd43om04visj8k6nmil",
						ClientSecret: "",
						Endpoint:     provider.Endpoint(),
						Scopes:       []string{oidc.ScopeOpenID, "email", "profile"},
					},
					AuthCodeOptions:        []oauth2.AuthCodeOption{oauth2.S256ChallengeOption(pkceVerifier), oauth2.ApprovalForce},
					TokenRequestOptions:    []oauth2.AuthCodeOption{oauth2.VerifierOption(pkceVerifier)},
					LocalServerReadyChan:   ready,
					LocalServerCertFile:    "",
					LocalServerKeyFile:     "",
					Logf:                   log.Printf,
					LocalServerBindAddress: []string{"localhost:52965"},
				}

				ctx := context.Background()
				eg, egCtx := errgroup.WithContext(ctx)
				var token *oauth2.Token

				eg.Go(func() error {
					select {
					case url := <-ready:
						log.Printf("Open %s", url)
						if err := browser.OpenURL(url); err != nil {
							log.Printf("could not open the browser: %s", err)
						}
						return nil
					case <-egCtx.Done():
						return fmt.Errorf("context done while waiting for authorization: %w", ctx.Err())
					}
				})
				eg.Go(func() error {
					t, err := oauth2cli.GetToken(egCtx, cfg)
					if err != nil {
						return fmt.Errorf("could not get a token: %w", err)
					}
					token = t
					log.Printf("You got a valid token until %s", token.Expiry)
					return nil
				})
				if err := eg.Wait(); err != nil {
					log.Fatalf("authorization error: %s", err)
				}

				// Validate that the authenticated user has a RudderStack email
				// Create temporary AdminJWT to extract email from the token
				tempAdminJWT := api.NewAdminJWT(token.RefreshToken)
				email, err := tempAdminJWT.GetEmail(c.Context)
				if err != nil {
					return fmt.Errorf("failed to extract email from ID token: %w", err)
				}

				if !isRudderStackEmail(email) {
					return fmt.Errorf("❌ Access denied: Admin authentication is restricted to RudderStack email addresses (@rudderstack.com)\nYour email: %s", email)
				}

				fmt.Printf("✓ Email validation passed: %s\n", email)

				// Store refresh token in profile
				pm := profile.NewProfileManager()
				prof, err := pm.GetProfile(profile.Default)
				if err != nil {
					// Create new profile if it doesn't exist
					prof = profile.Profile{}
				}

				prof.AdminRefreshToken = token.RefreshToken
				if err := pm.StoreProfile(profile.Default, prof); err != nil {
					return fmt.Errorf("failed to save refresh token to profile: %w", err)
				}

				fmt.Printf("✓ Authentication successful!\n")
				fmt.Printf("✓ Refresh token saved to profile: %s\n", profile.Default)
				return nil
			},
		},
		{
			Name:  "token-info",
			Usage: "Display information about stored admin auth token",
			Flags: []cli.Flag{
				&cli.BoolFlag{
					Name:    "debug",
					Aliases: []string{"d"},
					Usage:   "Show detailed token information including actual token values",
				},
			},
			Action: func(c *cli.Context) error {
				debug := c.Bool("debug")

				pm := profile.NewProfileManager()
				prof, err := pm.GetProfile(profile.Default)
				if err != nil {
					return fmt.Errorf("failed to get profile: %w", err)
				}

				if prof.AdminRefreshToken == "" {
					fmt.Printf("No admin refresh token found in profile: %s\n", profile.Default)
					fmt.Println("Run 'rsi admin auth' to authenticate")
					return nil
				}

				// Create AdminJWT to check token status
				adminJWT := api.NewAdminJWT(prof.AdminRefreshToken)

				fmt.Printf("Admin Auth Token Information:\n")
				fmt.Printf("  Profile: %s\n", profile.Default)
				fmt.Printf("  Has Refresh Token: ✓\n")

				if debug {
					fmt.Printf("  Refresh Token: %s\n", prof.AdminRefreshToken)
				}

				fmt.Printf("  Access Token Expired: %t\n", adminJWT.IsExpired())

				// Try to get an ID token to verify it works
				idToken, err := adminJWT.IDToken(c.Context)
				if err != nil {
					fmt.Printf("  Status: ❌ Failed to refresh (%v)\n", err)
				} else {
					fmt.Printf("  Status: ✅ Working\n")

					// Extract and display email from ID token
					if email, emailErr := adminJWT.GetEmail(c.Context); emailErr == nil {
						fmt.Printf("  Authenticated Email: %s\n", email)
						if !isRudderStackEmail(email) {
							fmt.Printf("  ⚠️  Warning: Email is not from RudderStack domain\n")
						}
					}

					if debug && idToken != "" {
						fmt.Printf("  ID Token: %s\n", idToken)
					}
				}

				return nil
			},
		},
	},
}
