package commands

import (
	"fmt"
	"os"
	"path"

	"github.com/urfave/cli/v2"
)

const autocompleteFineName = "_rudder_rsi"

var Autocomplete = &cli.Command{
	Name:            "autocomplete",
	Usage:           "Setup autocomplete for zsh shell",
	SkipFlagParsing: true,
	Hidden:          true,
	Action: func(c *cli.Context) error {
		shell := path.Base(os.Getenv("SHELL"))
		if shell != "zsh" {
			return fmt.Errorf("autocomplete only works with zsh, not %s", shell)
		}

		home := os.Getenv("HOME")

		completionDir := path.Join(home, ".zsh", "completion")
		err := os.MkdirAll(completionDir, os.ModePerm)
		if err != nil {
			return err
		}

		cPath := path.Join(completionDir, autocompleteFineName)
		f, err := os.Create(cPath)
		if err != nil {
			return err
		}

		_, err = f.WriteString(zshCompletion)
		if err != nil {
			return err
		}
		fmt.Println("zsh autocomplete script installed in", cPath)

		fmt.Println("")
		fmt.Println("Add the following in your ~/.zshrc:")
		fmt.Println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
		fmt.Printf("\n\nPROG=%s _CLI_ZSH_AUTOCOMPLETE_HACK=1 source %s \n\n\n", c.App.Name, cPath)
		fmt.Println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
		fmt.Println("")
		fmt.Println("")

		return nil
	},
}

const zshCompletion = `
#compdef $PROG

_cli_zsh_autocomplete() {

  local -a opts
  local cur
  cur=${words[-1]}
  if [[ "$cur" == "-"* ]]; then
    opts=("${(@f)$(_CLI_ZSH_AUTOCOMPLETE_HACK=1 ${words[@]:0:#words[@]-1} ${cur} --generate-bash-completion)}")
  else
    opts=("${(@f)$(_CLI_ZSH_AUTOCOMPLETE_HACK=1 ${words[@]:0:#words[@]-1} --generate-bash-completion)}")
  fi

  if [[ "${opts[1]}" != "" ]]; then
    _describe 'values' opts
  else
    _files
  fi

  return
}

compdef _cli_zsh_autocomplete $PROG
`
