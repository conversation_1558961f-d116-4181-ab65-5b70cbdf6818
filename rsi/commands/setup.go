package commands

import (
	"context"
	"fmt"
	"strings"

	"github.com/manifoldco/promptui"
	"github.com/rudderlabs/rs-ai/rsi/api"
	"github.com/rudderlabs/rs-ai/rsi/profile"
	"github.com/urfave/cli/v2"
)

var Setup = &cli.Command{
	Name:  "setup",
	Usage: "Setup the workspace",
	Action: func(c *cli.Context) error {
		fmt.Println("Setting up...")

		// Ask for token
		var token string
		fmt.Println("You can generate a Personal Access Token from https://app.rudderstack.com/profile")
		fmt.Print("Enter your Personal Access Token: ")
		fmt.Scanln(&token)

		// Fetch available workspaces from the user's account
		auth := api.PATAuth(token)
		client := api.NewClient(auth)
		userDetails, err := client.GetUserDetails(context.Background())
		if err != nil {
			return fmt.Errorf("failed to fetch user details: %w", err)
		}

		// Create a list of active workspaces to display
		var activeWorkspaces []struct {
			ID          string
			Name        string
			Environment string
			Region      string
		}

		for _, ws := range userDetails.Workspaces {
			// Only include workspaces that are ACTIVE and not marked as DELETED
			if ws.Status == "ACTIVE" && !strings.Contains(ws.Name, "DEPRECATED") {
				activeWorkspaces = append(activeWorkspaces, struct {
					ID          string
					Name        string
					Environment string
					Region      string
				}{
					ID:          ws.ID,
					Name:        ws.Name,
					Environment: ws.Environment,
					Region:      ws.DefaultRegion,
				})
			}
		}

		if len(activeWorkspaces) == 0 {
			return fmt.Errorf("no active workspaces found for this account")
		}

		// Create a nice selection prompt
		templates := &promptui.SelectTemplates{
			Label:    "{{ . }}",
			Active:   "> {{ .Name | cyan }} ({{ .Environment | red }})",
			Inactive: "  {{ .Name | cyan }} ({{ .Environment | red }})",
			Selected: "> {{ .Name | red | cyan }}",
			Details: `
--------- Workspace ----------
{{ "Name:" | faint }}	{{ .Name }}
{{ "Environment (Region):" | faint }} 	{{ .Environment }} ({{ .Region }})
`,
		}

		prompt := promptui.Select{
			Label:     "Select a workspace",
			Items:     activeWorkspaces,
			Templates: templates,
			Size:      5,
		}

		i, _, err := prompt.Run()
		if err != nil {
			return fmt.Errorf("workspace selection failed: %w", err)
		}

		selectedWorkspace := activeWorkspaces[i]
		workspaceID := selectedWorkspace.ID

		// Create a new ProfileManager
		pm := profile.NewProfileManager()

		// Add the new profile
		if err := pm.StoreProfile(profile.Default, profile.Profile{
			Token:       token,
			WorkspaceID: workspaceID,
		}); err != nil {
			return fmt.Errorf("failed to add profile: %w", err)
		}

		fmt.Printf("Setup complete. Workspace '%s' saved as default profile.\n", selectedWorkspace.Name)
		return nil
	},
}
