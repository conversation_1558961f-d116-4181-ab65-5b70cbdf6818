module github.com/rudderlabs/rs-ai/rsi

go 1.24.2

require (
	github.com/aereal/jsondiff v0.4.1
	github.com/coreos/go-oidc/v3 v3.14.1
	github.com/fatih/color v1.18.0
	github.com/google/uuid v1.6.0
	github.com/int128/oauth2cli v1.16.0
	github.com/itchyny/gojq v0.12.17
	github.com/manifoldco/promptui v0.9.0
	github.com/mark3labs/mcp-go v0.27.1
	github.com/pkg/browser v0.0.0-20240102092130-5ac0b6a4141c
	github.com/rudderlabs/analytics-go/v4 v4.2.1
	github.com/rudderlabs/rudder-go-kit v0.50.2
	github.com/spf13/cast v1.8.0
	github.com/urfave/cli/v2 v2.27.6
	golang.org/x/oauth2 v0.30.0
	golang.org/x/sync v0.14.0
	gopkg.in/yaml.v2 v2.4.0
)

require (
	github.com/chzyer/readline v1.5.1 // indirect
	github.com/cpuguy83/go-md2man/v2 v2.0.7 // indirect
	github.com/go-jose/go-jose/v4 v4.0.5 // indirect
	github.com/hexops/gotextdiff v1.0.3 // indirect
	github.com/int128/listener v1.2.0 // indirect
	github.com/itchyny/timefmt-go v0.1.6 // indirect
	github.com/mattn/go-colorable v0.1.14 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/russross/blackfriday/v2 v2.1.0 // indirect
	github.com/segmentio/backo-go v1.1.0 // indirect
	github.com/tidwall/gjson v1.18.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/xrash/smetrics v0.0.0-20240521201337-686a1a2994c1 // indirect
	github.com/yosida95/uritemplate/v3 v3.0.2 // indirect
	golang.org/x/crypto v0.37.0 // indirect
	golang.org/x/sys v0.33.0 // indirect
)
