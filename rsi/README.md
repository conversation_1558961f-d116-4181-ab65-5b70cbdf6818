# RudderStack MCP Server

The RudderStack MCP (Model Completion Protocol) Server provides a bridge between AI assistants and the RudderStack platform, allowing AI models to interact with RudderStack resources through a standardized protocol.

> **⚠️ EXPERIMENTAL**: The RSI CLI is currently experimental and relies on unofficial endpoints. It may break unexpectedly with RudderStack API changes. Use at your own risk in non-production environments.

## Installation

Install the RSI CLI using Go:

```bash
go install github.com/rudderlabs/rs-ai/rsi@latest
```

### Setup

Before using the MCP server, you need to set up your RudderStack workspace profile:

```bash
rsi setup
```

This will guide you through setting up your authentication token and workspace ID.

### Admin Authentication

For admin-level operations, authenticate with your RudderStack credentials:

```bash
rsi admin auth
```

This:
- Should open a browser, initiating Google OAuth2 authentication flow
- Store your OAuth token in the config file `~/.rudder/profiles/default.yaml` (default profile)

To verify:

```bash
rsi admin search-workspaces -e leo 
```

Restart your MCP client, you should now have access to admin-tools.

Remember: VPN connection is required.

### Shell Autocomplete

The RSI CLI supports shell autocompletion for zsh. You can enable it with:

```bash
rsi autocomplete
```

After running the command, add the suggested line to your `.zshrc` or `.bashrc` file and reload your shell:

```bash
source ~/.zshrc
```

Once set up, you can press the TAB key while typing an RSI command to show available options and commands.

### MCP Server Installation

Install the MCP server integration with the following commands:

```bash
# Install for Claude Desktop
rsi mcp-server install --claude

# Install for Cline (VS Code extension)
rsi mcp-server install --cline

# Install for Cursor
rsi mcp-server install --cursor

# Install for all supported platforms
rsi mcp-server install --all
```

## Overview

The MCP server exposes a set of tools that enable AI assistants like Claude, Cline, and Cursor to:

- List sources and destinations from your RudderStack workspace
- Send test events to your data pipeline
- Monitor health metrics and errors
- Manage transformations and connections
- View live events flowing through your pipeline
- **Admin Operations**: Search workspaces, impersonate workspaces, and manage billing (requires admin authentication)

## Available Tools

The MCP server provides the following tools:

### User Tools

- **Source Management**
  - `list_sources` - List all sources in your workspace
  - `source_live_events` - View events flowing through a source in real-time
  - `send_event` - Send a test event to a source

- **Destination Management**
  - `list_destinations` - List all destinations in your workspace
  - `destination_live_events` - View events flowing through a destination
  - `health_metrics` - Get performance metrics for destinations
  - `stream_errors` - View error details for a specific destination
  - `sample_error_event` - Get a sample of an error event

- **Transformation Management**
  - `list_transformations` - List all transformations
  - `get_transformation` - View details of a specific transformation
  - `transformation_test_new` - Test a new transformation
  - `transformation_test_existing` - Test an existing transformation
  - `upsert_transformation` - Create or update a transformation
  - `sample_transformations` - View sample transformation templates

- **Connection Management**
  - `list_connections` - View all connections between sources and destinations
  - `connect_transformation_destination` - Link a transformation to a destination

- **User Management**
  - `user_details` - Get current user information and available workspaces
  - `switch_workspace` - Switch to a different workspace
  - `audit_logs` - Get audit logs for the current workspace

### Admin Tools (Requires Admin Authentication)

- **Workspace Management**
  - `admin_search_workspaces` - Search for workspaces by various criteria (email, namespace, workspace ID, etc.)
  - `admin_impersonate_workspace` - Impersonate a workspace for admin operations, a limited set of user tools can be used as the impersonated workspace.

- **Billing Management**
  - `list_billing_plans` - View all available billing plans and features

## Usage

To start the MCP server:

```bash
rsi mcp-server
```

Once installed and running, AI assistants can access and interact with RudderStack resources through the MCP protocol.

## CLI Functionality

The RudderStack CLI provides comprehensive management capabilities beyond the MCP server:

### Workspace Management
- Configure and switch between multiple workspace profiles
- Securely store authentication tokens using Personal Access Token (PAT) authentication

### Admin Commands
- **Search Workspaces**: Find workspaces by email, namespace, workspace ID, organization ID, source/destination IDs, or types
- **Authentication**: Secure OAuth2-based admin authentication with email validation
- **Token Management**: View and manage admin authentication tokens

Example admin commands:

```bash
# Authenticate as admin
rsi admin auth

# Search workspaces by email
rsi admin search-workspaces --email <EMAIL>

# Search by workspace ID
rsi admin search-workspaces --workspace-id ws_123456

# Check admin token status
rsi admin token-info
```

### Source Commands
- Create, list, and manage data sources
- View source details and configurations

### Destination Commands
- Create and configure destinations
- View destination performance and diagnostics

### Transformation Commands
- Create, edit, and test JavaScript transformations
- Deploy transformations to your production pipeline

### Connection Management
- Connect sources to destinations
- Apply transformations to specific connections

Example commands:

```bash
# Set up and configure a new profile
rsi setup

# List all sources in your workspace
rsi sources list

# View transformation details
rsi transformations get <transformation-id>

# List all connections
rsi connections list
```

## Uninstalling

To remove the MCP server integration:

```bash
# Uninstall from a specific platform
rsi mcp-server uninstall --claude
rsi mcp-server uninstall --cline
rsi mcp-server uninstall --cursor

# Uninstall from all platforms
rsi mcp-server uninstall --all
```