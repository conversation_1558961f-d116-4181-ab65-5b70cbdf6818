package main

import (
	"fmt"
	"log"
	"os"

	"github.com/urfave/cli/v2"

	"github.com/rudderlabs/rs-ai/rsi/commands"
	"github.com/rudderlabs/rs-ai/rsi/profile"
)

type Source struct {
	ID               string `json:"id"`
	Name             string `json:"name"`
	Write<PERSON>ey         string `json:"writeKey"`
	Enabled          bool   `json:"enabled"`
	SourceDefinition struct {
		DisplayName string `json:"displayName"`
	} `json:"sourceDefinition"`
}

type Destination struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

func main() {
	pm := profile.NewProfileManager()

	app := &cli.App{
		Name:                   "rsi",
		Usage:                  "A CLI for interacting with the Rudder API",
		EnableBashCompletion:   true,
		UseShortOptionHandling: true,
		Flags: []cli.Flag{
			&cli.StringFlag{
				Name:    "token",
				EnvVars: []string{"RUDDER_TOKEN"},
			},
			&cli.StringFlag{
				Name:    "workspace-id",
				EnvVars: []string{"RUDDER_WORKSPACE_ID"},
			},
			&cli.StringFlag{
				Name:  "profile",
				Usage: "Profile to use from the config file",
				Value: profile.Default,
			},
		},
		Commands: []*cli.Command{
			commands.Sources,
			commands.Destinations,
			commands.Setup,
			commands.MCP,
			commands.Connections,
			commands.Transformations,
			commands.Autocomplete,
			commands.Admin,
		},
		Before: func(c *cli.Context) error {
			// Skip configuration loading for the setup command or autocomplete
			if c.Args().First() == "autocomplete" || c.Args().First() == "setup" {
				return nil
			}
			return loadConfig(c, pm)
		},
	}

	err := app.Run(os.Args)
	if err != nil {
		log.Fatal(err)
	}
}

func loadConfig(c *cli.Context, pm *profile.ProfileManager) error {
	profileName := c.String("profile")
	prof, err := pm.GetProfile(profileName)
	if err != nil {
		return fmt.Errorf("failed to load profile: %w", err)
	}

	// Command-line args take precedence over profile values
	if c.String("token") == "" {
		if err := c.Set("token", prof.Token); err != nil {
			return fmt.Errorf("failed to set token: %w", err)
		}
	}
	if c.String("workspace-id") == "" {
		if err := c.Set("workspace-id", prof.WorkspaceID); err != nil {
			return fmt.Errorf("failed to set workspace-id: %w", err)
		}
	}

	// Ensure we have both token and workspace-id
	if c.String("token") == "" || c.String("workspace-id") == "" {
		return fmt.Errorf("both token and workspace-id must be provided either via command-line arguments or in the profile")
	}

	return nil
}
