version: 2
project_name: rudder-rsi
builds:
  - env:
      - CGO_ENABLED=0
    goos:
      - linux
      - darwin
      - windows
    goarch:
      - amd64
      - arm64
    ldflags:
      - -s -w -X github.com/rudderstack/rs-ai/rsi/internal/version.Version={{.Version}}
    main: ./main.go

archives:
  - formats: [ 'tar.gz' ]
    name_template: >-
      rsi-
      {{- if eq .Os "darwin" }}mac-
      {{- else if eq .Os "windows" }}windows-
      {{- else if eq .Os "linux" }}linux-{{end}}
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "#86" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    format_overrides:
      - goos: windows
      - formats: [ 'zip' ]
checksum:
  name_template: "checksums.txt"
snapshot:
  version_template: "0.0.0-{{ .Timestamp }}"
brews:
  - repository:
      owner: rudderstack
      name: homebrew-tap

changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^doc:"
      - "^test:"
      - "^ci:"
      - "^ignore:"
      - "^example:"
      - "^wip:"