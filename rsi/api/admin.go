package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"os"
)

const (
	DefaultAdminAPIBaseURL = "https://admin-api.rudderstack.com"
)

type AdminClient struct {
	Email string
	Auth  Auth

	BaseURL    string
	HttpClient *http.Client
}

type AdminClientOpts func(*AdminClient)

func WithBaseURL(url string) AdminClientOpts {
	return func(c *AdminClient) {
		c.BaseURL = url
	}
}

func FromEnv(prefix string) AdminClientOpts {
	return func(c *AdminClient) {
		if url := os.Getenv(prefix + "_ADMIN_API_BASE_URL"); url != "" {
			c.BaseURL = url
		}
	}
}

func NewAdminClient(email string, auth Auth, opts ...AdminClientOpts) *AdminClient {
	client := &AdminClient{
		Email:      email,
		Auth:       auth,
		HttpClient: &http.Client{},
		BaseURL:    DefaultAdminAPIBaseURL,
	}
	for _, opt := range opts {
		opt(client)
	}
	return client
}

func (c *AdminClient) getAdminAPI(path string) (*http.Response, error) {
	req, err := http.NewRequest("GET", c.BaseURL+path, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("admin-email", c.Email)

	err = c.Auth.AuthHTTPRequest(req)
	if err != nil {
		return nil, fmt.Errorf("request authentication: %w", err)
	}

	return c.HttpClient.Do(req)
}

func (c *AdminClient) SearchWorkspaces(searchBy SearchBy, value string) ([]AdminWorkspace, error) {
	query := url.Values{}
	query.Add(string(searchBy), value)

	resp, err := c.getAdminAPI("/admin/workspaces?" + query.Encode())
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("search workspaces failed with status: %d", resp.StatusCode)
	}

	var workspaces []AdminWorkspace
	if err := json.NewDecoder(resp.Body).Decode(&workspaces); err != nil {
		return nil, err
	}

	return workspaces, nil
}

func (c *AdminClient) WorkspaceTenantInfo(workspaceID string) (OrgTenantInfo, error) {
	resp, err := c.getAdminAPI(fmt.Sprintf("/workspaces/%s/tenant", workspaceID))
	if err != nil {
		return OrgTenantInfo{}, fmt.Errorf("failed to get tenant info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return OrgTenantInfo{}, fmt.Errorf("get tenant info failed with status: %d", resp.StatusCode)
	}

	var info OrgTenantInfo
	if err := json.NewDecoder(resp.Body).Decode(&info); err != nil {
		return OrgTenantInfo{}, fmt.Errorf("failed to decode tenant info: %w", err)
	}

	return info, nil
}

func (c *AdminClient) WorkspacePlanFeatures(workspaceID string, planID string) (map[string]interface{}, error) {
	resp, err := c.getAdminAPI(fmt.Sprintf("/workspaces/%s/plan-features?planId=%s", workspaceID, planID))
	if err != nil {
		return nil, fmt.Errorf("failed to get plan features: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get plan features failed with status: %d", resp.StatusCode)
	}

	var features map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&features); err != nil {
		return nil, fmt.Errorf("failed to decode plan features: %w", err)
	}

	return features, nil
}

func (c *AdminClient) ImpersonateWorkspace(workspaceID string) (*Workspace, error) {
	// TODO: check if workspaceID is valid

	return &Workspace{
		Client: Client{
			auth: &ImpersonateAuth{
				RefreshEndpoint: c.BaseURL + "/admin/refreshToken",
				AdminEmail:      c.Email,
				JWT:             "", // empty, refreshToken can be used to get a new JWT
				httpClient:      c.HttpClient,
			},
			baseURL:    c.BaseURL,
			httpClient: c.HttpClient,
		},
		workspaceID: workspaceID,
	}, nil
}
