package api

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"golang.org/x/sync/singleflight"
)

type Auth interface {
	AuthHTTPRequest(req *http.Request) error
}

type ImpersonateAuth struct {
	RefreshEndpoint string
	JWT             string
	AdminEmail      string

	expiresAt  time.Time
	mu         sync.RWMutex
	httpClient *http.Client
	group      singleflight.Group
}

type tokenResponse struct {
	Token string `json:"token"`
	Email string `json:"email"`
}

type jwtClaims struct {
	Email              string         `json:"email"`
	ProviderUserID     string         `json:"providerUserId"`
	UserID             string         `json:"userId"`
	WorkspaceRoles     map[string]any `json:"workspaceRoles"`
	TokenVersion       int            `json:"tokenVersion"`
	Role               string         `json:"role"`
	IsRudderstackAdmin bool           `json:"isRudderstackAdmin"`
	AdminEmail         string         `json:"adminEmail"`
	IssuedAt           int64          `json:"iat"`
	ExpiresAt          int64          `json:"exp"`
}

func NewImpersonateAuth(refreshEndpoint, jwt, adminEmail string) *ImpersonateAuth {
	return &ImpersonateAuth{
		RefreshEndpoint: refreshEndpoint,
		JWT:             jwt,
		AdminEmail:      adminEmail,
		httpClient:      &http.Client{},
	}
}

func (a *ImpersonateAuth) refreshTokenIfNeeded() error {
	a.mu.RLock()
	needsRefresh := time.Now().Add(time.Minute * 5).After(a.expiresAt)
	a.mu.RUnlock()

	if !needsRefresh {
		return nil
	}

	// Use singleflight to deduplicate concurrent refresh requests
	_, err, _ := a.group.Do("refresh", func() (interface{}, error) {
		// Double check after acquiring through singleflight
		a.mu.RLock()
		stillNeedsRefresh := time.Now().Add(time.Minute * 5).After(a.expiresAt)
		a.mu.RUnlock()

		if !stillNeedsRefresh {
			return nil, nil
		}

		// Make refresh request
		req, err := http.NewRequest(http.MethodGet, a.RefreshEndpoint, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to create refresh request: %w", err)
		}

		req.Header.Set("admin-email", a.AdminEmail)

		resp, err := a.httpClient.Do(req)
		if err != nil {
			return nil, fmt.Errorf("failed to execute refresh request: %w", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return nil, fmt.Errorf("token refresh failed with status code: %d", resp.StatusCode)
		}

		var tokenResp tokenResponse
		if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
			return nil, fmt.Errorf("failed to decode refresh response: %w", err)
		}

		// Parse JWT to get expiration time
		parts := strings.Split(tokenResp.Token, ".")
		if len(parts) != 3 {
			return nil, fmt.Errorf("invalid JWT format")
		}

		payload, err := base64.RawURLEncoding.DecodeString(parts[1])
		if err != nil {
			return nil, fmt.Errorf("failed to decode JWT payload: %w", err)
		}

		var claims jwtClaims
		if err := json.Unmarshal(payload, &claims); err != nil {
			return nil, fmt.Errorf("failed to parse JWT claims: %w", err)
		}

		// Update token and expiration under lock
		a.mu.Lock()
		a.JWT = tokenResp.Token
		a.expiresAt = time.Unix(claims.ExpiresAt, 0)
		a.mu.Unlock()

		return nil, nil
	})

	return err
}

func (a *ImpersonateAuth) AuthHTTPRequest(req *http.Request) error {
	if err := a.refreshTokenIfNeeded(); err != nil {
		return fmt.Errorf("failed to refresh token: %w", err)
	}

	a.mu.RLock()
	token := a.JWT
	a.mu.RUnlock()

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	return nil
}
