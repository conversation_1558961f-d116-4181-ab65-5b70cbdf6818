package api

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/coreos/go-oidc/v3/oidc"
	"golang.org/x/oauth2"
	"golang.org/x/sync/singleflight"
)

const (
	// Cognito configuration
	cognitoProviderURL = "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_mkh5sGdqx"
	clientID           = "3pff4gmpd43om04visj8k6nmil"
)

// AdminJWT handles admin JWT authentication using refresh tokens
type AdminJWT struct {
	refreshToken string

	// JIT initialized fields
	config   *oauth2.Config
	verifier *oidc.IDTokenVerifier
	initOnce sync.Once
	initErr  error

	// Cached access token, ID token and expiry
	idToken   string
	expiresAt time.Time
	mu        sync.RWMutex
	group     singleflight.Group
}

// NewAdminJWT creates a new AdminJWT instance with the given refresh token
// Uses JIT initialization to defer expensive OIDC provider creation
func NewAdminJWT(refreshToken string) *AdminJWT {
	return &AdminJWT{
		refreshToken: refreshToken,
	}
}

func (a *AdminJWT) init(ctx context.Context) error {
	a.initOnce.Do(func() {
		provider, err := oidc.NewProvider(ctx, cognitoProviderURL)
		if err != nil {
			a.initErr = fmt.Errorf("failed to create OIDC provider: %w", err)
			return
		}

		a.config = &oauth2.Config{
			ClientID:     clientID,
			ClientSecret: "",
			Endpoint:     provider.Endpoint(),
			Scopes:       []string{oidc.ScopeOpenID, "email", "profile"},
		}

		// Create ID token verifier for secure token validation
		a.verifier = provider.Verifier(&oidc.Config{
			ClientID: clientID,
		})
	})

	return a.initErr
}

// IDToken returns a valid ID token, refreshing if necessary
func (a *AdminJWT) IDToken(ctx context.Context) (string, error) {
	// JIT initialization of OAuth2 config
	if err := a.init(ctx); err != nil {
		return "", err
	}

	a.mu.RLock()
	needsRefresh := time.Now().Add(5 * time.Minute).After(a.expiresAt)
	a.mu.RUnlock()

	if !needsRefresh && a.idToken != "" {
		a.mu.RLock()
		token := a.idToken
		a.mu.RUnlock()
		return token, nil
	}

	// Use singleflight to deduplicate concurrent refresh requests
	result, err, _ := a.group.Do("refresh", func() (interface{}, error) {
		// Double check after acquiring through singleflight
		a.mu.RLock()
		stillNeedsRefresh := time.Now().Add(5 * time.Minute).After(a.expiresAt)
		currentToken := a.idToken
		a.mu.RUnlock()

		if !stillNeedsRefresh && currentToken != "" {
			return currentToken, nil
		}

		// Create a token with just the refresh token
		oldToken := &oauth2.Token{
			RefreshToken: a.refreshToken,
		}

		// Use OAuth2 token source to refresh
		tokenSource := a.config.TokenSource(ctx, oldToken)
		newToken, err := tokenSource.Token()
		if err != nil {
			return nil, fmt.Errorf("failed to refresh token: %w", err)
		}

		// Update cached token and expiry
		a.mu.Lock()
		a.expiresAt = newToken.Expiry
		// Update refresh token if it changed
		if newToken.RefreshToken != "" {
			a.refreshToken = newToken.RefreshToken
		}
		// Cache ID token if present
		if idToken, ok := newToken.Extra("id_token").(string); ok {
			a.idToken = idToken
		} else {
			return nil, fmt.Errorf("no ID token found in refresh response")
		}
		a.mu.Unlock()

		return a.idToken, nil
	})

	if err != nil {
		return "", err
	}

	return result.(string), nil
}

// AuthHTTPRequest implements the Auth interface to add authorization header to HTTP requests
func (a *AdminJWT) AuthHTTPRequest(req *http.Request) error {
	token, err := a.IDToken(req.Context())
	if err != nil {
		return fmt.Errorf("failed to get ID token: %w", err)
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	return nil
}

// IsExpired returns true if the cached access token is expired or will expire soon
func (a *AdminJWT) IsExpired() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return time.Now().Add(5 * time.Minute).After(a.expiresAt)
}

// GetRefreshToken returns the current refresh token
func (a *AdminJWT) GetRefreshToken() string {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.refreshToken
}

// GetEmail extracts and returns the email claim from the ID token
func (a *AdminJWT) GetEmail(ctx context.Context) (string, error) {
	idToken, err := a.IDToken(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get ID token: %w", err)
	}

	return a.extractEmailFromIDToken(ctx, idToken)
}

// extractEmailFromIDToken securely extracts email claim from ID token with proper verification
func (a *AdminJWT) extractEmailFromIDToken(ctx context.Context, idToken string) (string, error) {
	// Ensure OIDC verifier is initialized
	if err := a.init(ctx); err != nil {
		return "", fmt.Errorf("failed to initialize OIDC config: %w", err)
	}

	// Verify the ID token signature and claims
	token, err := a.verifier.Verify(ctx, idToken)
	if err != nil {
		return "", fmt.Errorf("failed to verify ID token: %w", err)
	}

	// Extract claims from the verified token
	var claims struct {
		Email string `json:"email"`
	}

	if err := token.Claims(&claims); err != nil {
		return "", fmt.Errorf("failed to extract claims from verified token: %w", err)
	}

	if claims.Email == "" {
		return "", fmt.Errorf("email claim not found in verified token")
	}

	return claims.Email, nil
}
