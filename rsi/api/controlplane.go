package api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

const (
	defaultAPIBaseURL = "https://api.rudderstack.com"
)

// Client represents the RudderStack API client
type Client struct {
	auth       Auth
	baseURL    string
	httpClient *http.Client
}

type Workspace struct {
	Client
	workspaceID string
}

// NewClient initializes and returns a new RudderStack API client
func NewClient(auth Auth) *Client {
	return &Client{
		auth:       auth,
		baseURL:    defaultAPIBaseURL,
		httpClient: &http.Client{},
	}
}

// GetUserDetails retrieves the details of the authenticated user
func (c *Client) GetUserDetails(ctx context.Context) (*UserDetails, error) {
	path := "getUser"

	var userDetails UserDetails
	err := c.getResource(path, nil, &userDetails)
	if err != nil {
		return nil, fmt.Errorf("fetching user details: %w", err)
	}

	return &userDetails, nil
}

func (c *Client) ForWorkspace(workspaceID string) *Workspace {
	return &Workspace{
		Client:      *c,
		workspaceID: workspaceID,
	}
}

// getResource makes a GET request to the specified path with headers and decodes the response into responseStruct
func (c *Client) getResource(path string, headers map[string]string, responseStruct interface{}) error {
	req, err := http.NewRequest(http.MethodGet, c.baseURL+"/"+path, nil) // Default method is GET
	if err != nil {
		return fmt.Errorf("error creating request: %w", err)
	}

	err = c.auth.AuthHTTPRequest(req)
	if err != nil {
		return fmt.Errorf("error authenticating request: %w", err)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("User-Agent", "rudder-cli/0.1")

	for key, value := range headers {
		req.Header.Add(key, value)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(io.LimitReader(resp.Body, 2048))
		return fmt.Errorf("API request failed (code: %d): %s", resp.StatusCode, string(body))
	}

	return json.NewDecoder(resp.Body).Decode(responseStruct) // Decode the response body directly into the provided struct
}

// postResource makes a POST request to the specified path with the given body and decodes the response into responseStruct
func (c *Client) postResource(path string, body interface{}, responseStruct interface{}) error {
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("error marshaling request body: %w", err)
	}
	req, err := http.NewRequest(http.MethodPost, "https://api.rudderstack.com/"+path, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("error creating request: %w", err)
	}

	err = c.auth.AuthHTTPRequest(req)
	if err != nil {
		return fmt.Errorf("error authenticating request: %w", err)
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("User-Agent", "rudder-cli/0.1")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {

		var errorResponse struct {
			Error string `json:"error"`
		}
		err = json.NewDecoder(resp.Body).Decode(&errorResponse)
		if err == nil {
			return fmt.Errorf("API error: %s", errorResponse.Error)
		}

		body, _ := io.ReadAll(io.LimitReader(resp.Body, 2048))
		return fmt.Errorf("API request failed (code: %d): %s", resp.StatusCode, string(body))
	}

	// if responseStruct is nil, return nil
	if responseStruct == nil {
		return nil
	}

	return json.NewDecoder(resp.Body).Decode(responseStruct)
}

func (w *Workspace) ID() string {
	return w.workspaceID
}

// ListSources retrieves all sources in the workspace
func (c *Workspace) ListSources() ([]Source, error) {
	var response struct {
		Sources []Source `json:"sources"`
	}

	err := c.getResource(fmt.Sprintf("workspaces/%s/sources", c.workspaceID), nil, &response) // Updated function call
	if err != nil {
		return nil, err
	}

	return response.Sources, nil
}

// ListDestinations retrieves all destinations in the workspace
func (c *Workspace) ListDestinations() ([]Destination, error) {
	type destinationAPI struct {
		ID                    string `json:"id"`
		Name                  string `json:"name"`
		DestinationDefinition struct {
			DisplayName string `json:"displayName"`
		} `json:"destinationDefinition"`
		CreatedAt time.Time `json:"createdAt"`
		UpdatedAt time.Time `json:"updatedAt"`
	}

	var response struct {
		Destinations []destinationAPI `json:"destinations"`
	}

	err := c.getResource(fmt.Sprintf("workspaces/%s/destinations", c.workspaceID), nil, &response)
	if err != nil {
		return nil, err
	}

	destinations := make([]Destination, 0, len(response.Destinations))
	for _, d := range response.Destinations {
		dest := Destination{
			ID:        d.ID,
			Name:      d.Name,
			Type:      d.DestinationDefinition.DisplayName,
			CreatedAt: d.CreatedAt,
			UpdatedAt: d.UpdatedAt,
		}
		destinations = append(destinations, dest)
	}

	return destinations, nil
}

// SourceLiveEvents fetches live events for a specific source starting from a given event ID
func (c *Workspace) SourceLiveEvents(source Source, from int64) ([]Event, error) {
	path := fmt.Sprintf("workspaces/%s/eventUploads?id=%d&from=%d", c.workspaceID, 0, from)

	err := c.toggleEventUpload(source.ID, true)
	if err != nil {
		return nil, fmt.Errorf("error toggling event upload: %w", err)
	}

	var response []Event
	err = c.getResource(path, map[string]string{"X-WRITE-KEY": source.WriteKey}, &response)
	if err != nil {
		return nil, fmt.Errorf("error fetching live events: %w", err)
	}

	return response, nil
}

// DestinationLiveEvents fetches live events for a specific destination starting from a given event ID
func (c *Workspace) DestinationLiveEvents(destination Destination, from int64) ([]DestinationLiveEvent, error) {
	path := fmt.Sprintf("workspaces/%s/eventDeliveryStatus?id=%d&from=%d", c.workspaceID, 0, from)
	err := c.toggleDestinationEventUpload(destination.ID, true)
	if err != nil {
		return nil, fmt.Errorf("error toggling event upload: %w", err)
	}

	headers := map[string]string{
		"X-destination-id": destination.ID,
	}

	var response []DestinationLiveEvent
	err = c.getResource(path, headers, &response)
	if err != nil {
		return nil, fmt.Errorf("error fetching live events: %w", err)
	}

	return response, nil
}

// toggleEventUpload enables or disables event uploads for a source
func (c *Workspace) toggleEventUpload(sourceID string, enable bool) error {
	path := fmt.Sprintf("workspaces/%s/sources/%s/toggleEventUpload", c.workspaceID, sourceID)

	body := struct {
		EventUpload bool `json:"eventUpload"`
	}{
		EventUpload: enable,
	}

	return c.postResource(path, body, nil)
}

// toggleDestinationEventUpload enables or disables event uploads for a destination
func (c *Workspace) toggleDestinationEventUpload(destinationID string, enable bool) error {
	path := fmt.Sprintf("workspaces/%s/destinations/%s/toggleEventDelivery", c.workspaceID, destinationID)

	body := struct {
		EventDelivery bool `json:"eventDelivery"`
	}{
		EventDelivery: enable,
	}

	return c.postResource(path, body, nil)
}

// WorkspaceSettings retrieves the settings of the workspace
func (c *Workspace) WorkspaceSettings() (WorkspaceSettings, error) {
	path := fmt.Sprintf("workspaces/%s/settings", c.workspaceID)

	var response struct {
		Workspace WorkspaceSettings `json:"workspace"`
	}

	err := c.getResource(path, nil, &response)
	if err != nil {
		return WorkspaceSettings{}, err
	}

	return response.Workspace, nil
}

// StreamHealthMetrics retrieves the health metrics of event streams for a given time range and region
func (c *Workspace) StreamHealthMetrics(ctx context.Context, start time.Time, end time.Time) ([]HealthMetrics, error) {
	// Set query parameters with exact time format required: YYYY-MM-DDThh:mm:ss.000Z
	q := url.Values{}
	q.Set("start", start.UTC().Format("2006-01-02T15:04:05.000Z"))
	q.Set("end", end.UTC().Format("2006-01-02T15:04:05.000Z"))

	path := fmt.Sprintf("workspaces/%s/eventStreamHealth?%s", c.workspaceID, q.Encode())

	var response map[string]struct {
		SuccessSum int64 `json:"successSum"`
		AbortSum   int64 `json:"abortSum"`
	}

	err := c.getResource(path, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("fetching event stream health %q: %w", path, err)
	}

	healthMetrics := make([]HealthMetrics, 0, len(response))
	for destinationID, metrics := range response {
		healthMetrics = append(healthMetrics, HealthMetrics{
			DestinationID: destinationID,
			SuccessSum:    metrics.SuccessSum,
			AbortSum:      metrics.AbortSum,
		})
	}
	return healthMetrics, nil
}

// DestinationEventErrors retrieves error events for a specific destination
func (c *Workspace) DestinationEventErrors(ctx context.Context, destinationID string, start time.Time) ([]EventStreamError, error) {
	// Set query parameters with exact time format required: YYYY-MM-DDThh:mm:ss.000Z
	q := url.Values{}
	q.Set("start", start.UTC().Format("2006-01-02T15:04:05.000Z"))

	path := fmt.Sprintf("workspaces/%s/destinations/%s/eventStreamErrors?%s",
		c.workspaceID, destinationID, q.Encode())

	var response []struct {
		EventStreamError
		LastSeen int64 `json:"lastSeen"`
	}
	err := c.getResource(path, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("fetching event stream errors for destination %s: %w", destinationID, err)
	}

	eventStreamErrors := make([]EventStreamError, 0, len(response))
	for _, eventStreamError := range response {
		eventStreamError.EventStreamError.DestinationID = destinationID
		eventStreamError.EventStreamError.ReportedAt = time.Unix(eventStreamError.LastSeen/1000, 0).UTC()
		eventStreamErrors = append(eventStreamErrors, eventStreamError.EventStreamError)
	}

	return eventStreamErrors, nil
}

// GetSampleEvent retrieves a sample event and its response for a specific error case
func (c *Workspace) GetSampleEvent(ctx context.Context, eventError EventStreamError) (*SampleEventResponse, error) {
	q := url.Values{}
	q.Set("sourceId", eventError.SourceID)
	q.Set("destinationId", eventError.DestinationID)
	q.Set("eventName", eventError.EventName)
	q.Set("eventType", eventError.EventType)
	q.Set("reportedAt", eventError.ReportedAt.Format("2006-01-02T15:04:05.000Z"))
	q.Set("statusCode", fmt.Sprintf("%d", eventError.StatusCode))
	q.Set("reportedBy", eventError.ReportedBy)

	path := fmt.Sprintf("workspaces/%s/sampleEvent?%s", c.workspaceID, q.Encode())

	var response SampleEventResponse
	err := c.getResource(path, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("fetching sample event: %w", err)
	}

	return &response, nil
}

// ListConnections retrieves all connections in the workspace
func (c *Workspace) ListConnections() ([]Connection, error) {
	var response struct {
		Connections []Connection `json:"connections"`
	}

	err := c.getResource(fmt.Sprintf("workspaces/%s/connections", c.workspaceID), nil, &response)
	if err != nil {
		return nil, err
	}

	return response.Connections, nil
}

// TransformationTest calls the transformation test API endpoint
func (c *Workspace) TransformationTest(ctx context.Context, req TransformationTestRequest) (TransformationTestResponse, error) {
	path := fmt.Sprintf("workspaces/%s/transformation/test", c.workspaceID)

	var resp TransformationTestResponse
	err := c.postResource(path, req, &resp)
	if err != nil {
		return TransformationTestResponse{}, err
	}
	return resp, nil
}

// SampleTransformations returns a list of sample transformations provided by rudderstack.
func (c *Workspace) SampleTransformations(ctx context.Context) ([]SampleTransformation, error) {
	path := fmt.Sprintf("workspaces/%s/rudderstackTransformations", c.workspaceID)

	var resp struct {
		RudderstackTransformations []SampleTransformation `json:"rudderstackTransformations"`
	}
	err := c.getResource(path, nil, &resp)
	if err != nil {
		return nil, err
	}
	return resp.RudderstackTransformations, nil
}

// SaveTransformation saves or updates a transformation to the workspace
func (c *Workspace) SaveTransformation(ctx context.Context, req SaveTransformationRequest) (SaveTransformationResponse, error) {
	var (
		path   string
		method string
	)
	if req.ID != "" {
		path = fmt.Sprintf("workspaces/%s/transformations/%s?publish=true", c.workspaceID, req.ID)
		method = http.MethodPost
	} else {
		path = fmt.Sprintf("workspaces/%s/transformations?publish=true", c.workspaceID)
		method = http.MethodPost
	}
	req.UpgradeTransformation = true

	jsonBody, err := json.Marshal(req)
	if err != nil {
		return SaveTransformationResponse{}, err
	}

	reqObj, err := http.NewRequestWithContext(ctx, method, "https://api.rudderstack.com/"+path, bytes.NewBuffer(jsonBody))
	if err != nil {
		return SaveTransformationResponse{}, err
	}
	err = c.auth.AuthHTTPRequest(reqObj)
	if err != nil {
		return SaveTransformationResponse{}, fmt.Errorf("request authentication: %w", err)
	}
	reqObj.Header.Add("Content-Type", "application/json")
	reqObj.Header.Add("User-Agent", "rudder-cli/0.1")

	respObj, err := c.httpClient.Do(reqObj)
	if err != nil {
		return SaveTransformationResponse{}, err
	}
	defer respObj.Body.Close()

	if respObj.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(io.LimitReader(respObj.Body, 256))
		return SaveTransformationResponse{}, fmt.Errorf("API request failed with status %d: %s", respObj.StatusCode, string(body))
	}

	var resp SaveTransformationResponse
	if err := json.NewDecoder(respObj.Body).Decode(&resp); err != nil {
		return SaveTransformationResponse{}, err
	}
	return resp, nil
}

// ConnectTransformationToDestination connects a transformation to a destination
func (c *Workspace) ConnectTransformationToDestination(ctx context.Context, transformationID, destinationID string) error {
	path := fmt.Sprintf("workspaces/%s/transformations/%s/connectToDestination", c.workspaceID, transformationID)

	type ReqPayloadConfig struct {
		EnableForCloudMode                               bool `json:"enableForCloudMode"`
		EnableForDeviceMode                              bool `json:"enableForDeviceMode"`
		PropagateEventsUntransformedOnErrorForDeviceMode bool `json:"propagateEventsUntransformedOnErrorForDeviceMode"`
		PropagateEventsUntransformedOnErrorForCloudMode  bool `json:"propagateEventsUntransformedOnErrorForCloudMode"`
	}

	type ReqPayload struct {
		DestinationID string           `json:"destinationId"`
		Config        ReqPayloadConfig `json:"config"`
	}

	return c.postResource(path, ReqPayload{
		DestinationID: destinationID,
		Config: ReqPayloadConfig{
			EnableForCloudMode:  true,
			EnableForDeviceMode: false,
			PropagateEventsUntransformedOnErrorForDeviceMode: false,
			PropagateEventsUntransformedOnErrorForCloudMode:  false,
		},
	}, nil)
}

// ListTransformations retrieves all transformations in the workspace
func (c *Workspace) ListTransformations(ctx context.Context) ([]Transformation, error) {
	path := fmt.Sprintf("workspaces/%s/transformations?workspace_id=%s", c.workspaceID, c.workspaceID)

	type apiResponse struct {
		Transformations []struct {
			ID           string                   `json:"id"`
			Name         string                   `json:"name"`
			Description  string                   `json:"description"`
			CreatedAt    string                   `json:"createdAt"`
			UpdatedAt    string                   `json:"updatedAt"`
			VersionID    string                   `json:"versionId"`
			Language     string                   `json:"language"`
			Destinations []map[string]interface{} `json:"destinations"`
		} `json:"transformations"`
	}

	var response apiResponse
	err := c.getResource(path, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("fetching transformations: %w", err)
	}

	transformations := make([]Transformation, 0, len(response.Transformations))
	for _, t := range response.Transformations {
		// Extract destination IDs
		destIDs := make([]string, 0, len(t.Destinations))
		for _, d := range t.Destinations {
			if id, ok := d["id"].(string); ok {
				destIDs = append(destIDs, id)
			}
		}

		transformations = append(transformations, Transformation{
			ID:             t.ID,
			Name:           t.Name,
			Description:    t.Description,
			CreatedAt:      t.CreatedAt,
			UpdatedAt:      t.UpdatedAt,
			VersionID:      t.VersionID,
			Language:       t.Language,
			DestinationIDs: destIDs,
		})
	}

	return transformations, nil
}

// GetTransformation retrieves a specific transformation by ID
func (c *Workspace) GetTransformation(ctx context.Context, transformationID string) (*TransformationDetail, error) {
	path := fmt.Sprintf("workspaces/%s/transformations/%s", c.workspaceID, transformationID)

	var apiResponse struct {
		ID           string                   `json:"id"`
		Name         string                   `json:"name"`
		Description  string                   `json:"description"`
		Code         string                   `json:"code"`
		CreatedAt    string                   `json:"createdAt"`
		UpdatedAt    string                   `json:"updatedAt"`
		VersionID    string                   `json:"versionId"`
		CodeVersion  string                   `json:"codeVersion"`
		Language     string                   `json:"language"`
		WorkspaceID  string                   `json:"workspaceId"`
		Config       map[string]interface{}   `json:"config"`
		Destinations []map[string]interface{} `json:"destinations"`
	}

	err := c.getResource(path, nil, &apiResponse)
	if err != nil {
		return nil, fmt.Errorf("fetching transformation %s: %w", transformationID, err)
	}

	// Extract destination IDs
	destIDs := make([]string, 0, len(apiResponse.Destinations))
	for _, d := range apiResponse.Destinations {
		if id, ok := d["id"].(string); ok {
			destIDs = append(destIDs, id)
		}
	}

	return &TransformationDetail{
		ID:             apiResponse.ID,
		Name:           apiResponse.Name,
		Description:    apiResponse.Description,
		Code:           apiResponse.Code,
		CreatedAt:      apiResponse.CreatedAt,
		UpdatedAt:      apiResponse.UpdatedAt,
		VersionID:      apiResponse.VersionID,
		CodeVersion:    apiResponse.CodeVersion,
		Language:       apiResponse.Language,
		WorkspaceID:    apiResponse.WorkspaceID,
		Config:         apiResponse.Config,
		DestinationIDs: destIDs,
	}, nil
}

// GetAuditLogs retrieves audit logs for the workspace with pagination support
func (c *Workspace) GetAuditLogs(ctx context.Context, skip, limit int, targetType string) ([]AuditLogEntry, error) {
	q := url.Values{}
	q.Set("skip", fmt.Sprintf("%d", skip))
	q.Set("limit", fmt.Sprintf("%d", limit))
	if targetType != "" {
		q.Set("targetType", targetType)
	}

	path := fmt.Sprintf("workspaces/%s/auditlog?%s", c.workspaceID, q.Encode())

	var response struct {
		Logs []AuditLogEntry `json:"logs"`
	}

	err := c.getResource(path, nil, &response)
	if err != nil {
		return nil, fmt.Errorf("fetching audit logs: %w", err)
	}

	return response.Logs, nil
}
