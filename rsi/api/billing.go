package api

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
)

const (
	defaultBillingBaseURL = "https://billing.rudderstack.com"
)

type BillingClient struct {
	BaseURL string
}

type Plan struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	DisplayName  string                 `json:"displayName"`
	StripePlanID *string                `json:"stripePlanId"`
	Features     map[string]interface{} `json:"features"`
	Deleted      bool                   `json:"deleted"`
	SelfServe    bool                   `json:"selfServe"`
	CreatedAt    string                 `json:"createdAt"`
	UpdatedAt    string                 `json:"updatedAt"`
	EventVolume  int                    `json:"eventVolume"`
}

type OrgTenantInfo struct {
	BillingVersion          string   `json:"billingVersion"`
	InvoiceEmails           []string `json:"invoiceEmails"`
	ID                      string   `json:"id"`
	OrgID                   string   `json:"orgId"`
	OrgName                 string   `json:"orgName"`
	OwnerEmail              string   `json:"ownerEmail"`
	BillingEnabled          bool     `json:"billingEnabled"`
	StripeCustomerID        string   `json:"stripeCustomerId"`
	CreatedAt               string   `json:"createdAt"`
	UpdatedAt               string   `json:"updatedAt"`
	CurrentPlanStartAt      string   `json:"currentPlanStartAt"`
	ContractEventVolume     *int     `json:"contractEventVolume"`
	SalesforceAccountStatus *string  `json:"salesforceAccountStatus"`
	AnnualizedEventVolume   *int     `json:"annualizedEventVolume"`
	PlanID                  string   `json:"planId"`
	PlanName                string   `json:"planName"`
	DefaultPaymentMethod    *string  `json:"defaultPaymentMethod"`
}

func NewBillingClient() *BillingClient {
	return &BillingClient{
		BaseURL: defaultBillingBaseURL,
	}
}

func (c *BillingClient) Plans() ([]Plan, error) {
	url := fmt.Sprintf("%s/v1/plan", c.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("unexpected status code %d: %s", resp.StatusCode, string(body))
	}

	var plans []Plan
	if err := json.NewDecoder(resp.Body).Decode(&plans); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return plans, nil
}

func (c *BillingClient) OrgTenantInfo(orgID string) (*OrgTenantInfo, error) {
	// Build URL with query parameters
	baseURL := fmt.Sprintf("%s/v1/organizations/%s/tenant", c.BaseURL, url.PathEscape(orgID))

	// Add query parameters
	params := url.Values{}
	params.Add("billing", "true")
	params.Add("deployment", "true")

	// Construct final URL
	fullURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// Create request
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Make request
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("unexpected status code %d: %s", resp.StatusCode, string(body))
	}

	// Decode response
	var tenantInfo OrgTenantInfo
	if err := json.NewDecoder(resp.Body).Decode(&tenantInfo); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &tenantInfo, nil
}
