package api

import (
	"bytes"
	"context"
	"embed"
	"fmt"
	"html/template"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/rudderlabs/rudder-go-kit/httputil"
)

//go:embed payloads/*
var payloads embed.FS

type DataplaneClient struct {
	dataPlaneURL string
	writeKey     string
	httpClient   *http.Client
}

func NewDataplaneClient(dataPlaneURL string, writeKey string) *DataplaneClient {
	return &DataplaneClient{
		dataPlaneURL: dataPlaneURL,
		writeKey:     writeKey,
		httpClient:   &http.Client{},
	}
}

func (c *DataplaneClient) SendEvent(ctx context.Context) error {
	url := fmt.Sprintf("%s/v1/batch", c.dataPlaneURL)
	t, err := template.New("batch.json").ParseFS(payloads, "payloads/batch.json")
	if err != nil {
		return err
	}

	anonymousId := uuid.New().String()
	buf := bytes.NewBuffer(nil)
	err = t.Execute(buf, map[string]string{
		"AnonymousId": anonymousId,
		"Timestamp":   time.Now().Format(time.RFC3339),
	})
	if err != nil {
		return err
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, buf)
	if err != nil {
		return err
	}

	req.SetBasicAuth(c.writeKey, "")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("AnonymousId", anonymousId)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer func() { httputil.CloseResponse(resp) }()
	b, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("status code: %d: %s", resp.StatusCode, string(b))
	}
	return nil
}
