package api

import (
	"encoding/json"
	"time"
)

type WorkspaceSettings struct {
	ID             string    `json:"id"`
	Name           string    `json:"name"`
	CreatedAt      time.Time `json:"createdAt"`
	DataPlaneURL   string    `json:"dataPlaneURL"`
	OrganizationID string    `json:"organizationId"`
	UserRoles      []struct {
		Roles struct {
			Organization string `json:"organization"`
		} `json:"roles"`
		User struct {
			ID    string `json:"id"`
			Email string `json:"email"`
			Name  string `json:"name"`
		} `json:"user"`
	} `json:"userRoles"`
	InvitedUsers []interface{} `json:"invitedUsers"`
	SSHConfig    struct {
		ID        string `json:"id"`
		PublicKey string `json:"publicKey"`
	} `json:"sshConfig"`
	Environment       string `json:"environment"`
	IsDefault         bool   `json:"isDefault"`
	EventAuditEnabled bool   `json:"eventAuditEnabled"`
	OrgSettings       struct {
		Name string `json:"name"`
	} `json:"orgSettings"`
	PlanName       string        `json:"planName"`
	DefaultRegion  string        `json:"defaultRegion"`
	Dataplanes     []interface{} `json:"dataplanes"`
	PIIPermissions struct {
		IsLocked bool `json:"isLocked"`
	} `json:"piiPermissions"`
}

type Source struct {
	ID               string `json:"id"`
	Name             string `json:"name"`
	WriteKey         string `json:"writeKey"`
	Enabled          bool   `json:"enabled"`
	SourceDefinition struct {
		DisplayName string `json:"displayName"`
	} `json:"sourceDefinition"`
}

type Event struct {
	ID        int64  `json:"id"`
	EventName string `json:"eventName"`
	EventType string `json:"eventType"`
	Event     struct {
		AnonymousID       string          `json:"anonymousId"`
		Channel           string          `json:"channel"`
		Context           json.RawMessage `json:"context"`
		Integrations      json.RawMessage `json:"integrations"`
		MessageID         string          `json:"messageId"`
		OriginalTimestamp string          `json:"originalTimestamp"`
		Properties        json.RawMessage `json:"properties"`
		ReceivedAt        string          `json:"receivedAt"`
		RequestIP         string          `json:"request_ip"`
		RudderID          string          `json:"rudderId"`
		SentAt            string          `json:"sentAt"`
		Type              string          `json:"type"`
	}
	EventTimeStamp string          `json:"eventTimeStamp"`
	ErrorCode      int             `json:"errorCode"`
	ErrorResponse  json.RawMessage `json:"errorResponse"`
	Version        string          `json:"version"`
	CreatedAt      string          `json:"createdAt"`
}

// Destination represents a destination in RudderStack
type Destination struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	Type      string    `json:"type"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type DestinationLiveEvent struct {
	ID            int64           `json:"id"`
	EventName     string          `json:"eventName"`
	EventType     string          `json:"eventType"`
	SentAt        string          `json:"sentAt"`
	Version       string          `json:"version"`
	DestinationID string          `json:"destinationId"`
	SourceID      string          `json:"sourceId"`
	Payload       json.RawMessage `json:"payload"`
	AttemptNum    int             `json:"attemptNum"`
	JobState      string          `json:"jobState"`
	ErrorCode     string          `json:"errorCode"`
	ErrorResponse struct {
		Response         string `json:"response"`
		FirstAttemptedAt string `json:"firstAttemptedAt"`
		ContentType      string `json:"content-type"`
	} `json:"errorResponse"`
	CreatedAt string `json:"createdAt"`
}

// HealthMetrics represents the metrics for a single stream
type HealthMetrics struct {
	DestinationID string `json:"destinationId"`
	SuccessSum    int64  `json:"successSum"`
	AbortSum      int64  `json:"abortSum"`
}

// EventStreamError represents an error event from a destination's event stream
type EventStreamError struct {
	Count         int64     `json:"count"`
	EventName     string    `json:"eventName"`
	EventType     string    `json:"eventType"`
	ReportedAt    time.Time `json:"reportedAt"`
	ReportedBy    string    `json:"reportedBy"`
	SourceID      string    `json:"sourceId"`
	DestinationID string    `json:"destinationId"`
	StatusCode    int       `json:"statusCode"`
}

// SampleEventResponse represents the response from the sample event endpoint
type SampleEventResponse struct {
	SampleEvent    string `json:"sampleEvent"`
	SampleResponse string `json:"sampleResponse"`
}

type Connection struct {
	ID            string    `json:"id"`
	SourceID      string    `json:"sourceId"`
	DestinationID string    `json:"destinationId"`
	WorkspaceID   string    `json:"workspaceId"`
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
	Enabled       bool      `json:"enabled"`
	Deleted       bool      `json:"deleted"`
}

// TransformationTestRequest represents the request payload for the transformation test API
// Example:
//
//	{
//	  code: "",
//	  codeVersion: 1,
//	  input: "",
//	  language: "javascript",
//	  upgradeTransformation: false
//	}
type TransformationTestRequest struct {
	Code                  string `json:"code"`
	CodeVersion           string `json:"codeVersion"`
	Input                 string `json:"input"`
	Language              string `json:"language"`
	UpgradeTransformation bool   `json:"upgradeTransformation"`
}

// TransformationTestResponse represents the response from the transformation test API
// Example:
//
//	{
//	  "transformedEvents": [ ... { output event object }],
//	  "logs": []
//	}
type TransformationTestResponse struct {
	TransformedEvents []map[string]interface{} `json:"transformedEvents"`
	Logs              []interface{}            `json:"logs"`
}

// SampleTransformation represents a transformation provided by rudderstack.
type SampleTransformation struct {
	ID          string `json:"id"`
	VersionID   string `json:"versionId"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Code        string `json:"code"`
	CodeVersion string `json:"codeVersion"`
	Language    string `json:"language"`
	Category    string `json:"category"`
	Icon        string `json:"icon"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
}

// SaveTransformationRequest represents the request payload for saving a new transformation
// Matches the sample request payload
type SaveTransformationRequest struct {
	ID                    string `json:"id,omitempty"`
	Name                  string `json:"name"`
	Description           string `json:"description"`
	Code                  string `json:"code"`
	UpgradeTransformation bool   `json:"upgradeTransformation"`
	WorkspaceID           string `json:"workspaceId"`
	Language              string `json:"language"`
	Events                string `json:"events"`
}

// SaveTransformationResponse represents the response from saving a new transformation
// Matches the sample response payload
type SaveTransformationResponse struct {
	Config           map[string]interface{} `json:"config"`
	LiveEventsConfig map[string]interface{} `json:"liveEventsConfig"`
	ID               string                 `json:"id"`
	CreatedAt        string                 `json:"createdAt"`
	UpdatedAt        string                 `json:"updatedAt"`
	VersionID        string                 `json:"versionId"`
	Name             string                 `json:"name"`
	Description      string                 `json:"description"`
	Code             string                 `json:"code"`
	SecretsVersion   *string                `json:"secretsVersion"`
	CreatedBy        string                 `json:"createdBy"`
	CodeVersion      string                 `json:"codeVersion"`
	WorkspaceID      string                 `json:"workspaceId"`
	Language         string                 `json:"language"`
}

// Transformation represents a transformation with essential fields
type Transformation struct {
	ID             string   `json:"id"`
	Name           string   `json:"name"`
	Description    string   `json:"description"`
	CreatedAt      string   `json:"createdAt"`
	UpdatedAt      string   `json:"updatedAt"`
	VersionID      string   `json:"versionId"`
	Language       string   `json:"language"`
	DestinationIDs []string `json:"destinations,omitempty"`
}

// TransformationDetail represents a detailed view of a transformation including code
type TransformationDetail struct {
	ID             string                 `json:"id"`
	Name           string                 `json:"name"`
	Description    string                 `json:"description"`
	Code           string                 `json:"code"`
	CreatedAt      string                 `json:"createdAt"`
	UpdatedAt      string                 `json:"updatedAt"`
	VersionID      string                 `json:"versionId"`
	CodeVersion    string                 `json:"codeVersion"`
	Language       string                 `json:"language"`
	WorkspaceID    string                 `json:"workspaceId"`
	Config         map[string]interface{} `json:"config"`
	DestinationIDs []string               `json:"destinations,omitempty"`
}

// UserDetails represents the user details response
type UserDetails struct {
	EnforcedMFA bool `json:"enforcedMfa"`
	CanSetupMFA bool `json:"canSetupMfa"`
	Workspaces  []struct {
		ID             string   `json:"id"`
		Name           string   `json:"name"`
		OrganizationID string   `json:"organizationId"`
		Environment    string   `json:"environment"`
		IsDefault      bool     `json:"isDefault"`
		Roles          []string `json:"roles"`
		DefaultRegion  string   `json:"defaultRegion"`
		Status         string   `json:"status"`
	} `json:"workspaces"`
	Organizations []struct {
		ID       string `json:"id"`
		Name     string `json:"name"`
		Role     string `json:"role"`
		PlanType string `json:"planType"`
	} `json:"organizations"`
	PhoneNumber    *string  `json:"phoneNumber"`
	MFAStatus      *string  `json:"mfaStatus"`
	Email          string   `json:"email"`
	ID             string   `json:"id"`
	Name           string   `json:"name"`
	Provider       string   `json:"provider"`
	ProviderUserID string   `json:"providerUserId"`
	PendingTasks   []string `json:"pendingTasks"`
}

// AuditLogUser represents a user in the audit log
type AuditLogUser struct {
	Email string `json:"email"`
	Name  string `json:"name"`
}

// AuditLogEntry represents a single audit log entry
type AuditLogEntry struct {
	ID         string       `json:"id"`
	User       AuditLogUser `json:"user"`
	UserID     string       `json:"userId"`
	CreatedAt  time.Time    `json:"createdAt"`
	IP         string       `json:"ip"`
	Target     string       `json:"target"`
	TargetType string       `json:"targetType"`
	Workspace  string       `json:"workspace"`
	Action     string       `json:"action"`
	Payload    struct {
		From json.RawMessage `json:"from,omitempty"`
		To   json.RawMessage `json:"to,omitempty"`
	} `json:"payload"`
}

type AuditPayload struct {
	From json.RawMessage `json:"from"`
	To   json.RawMessage `json:"to"`
	Name string          `json:"name"`
}

type AdminWorkspace struct {
	ID                     string  `json:"id"`
	Name                   string  `json:"name"`
	// Token                  string  `json:"token"` Don't expose this field, it is very sensitive
	OrganizationID         string  `json:"organizationId"`
	DataPlaneURL           string  `json:"dataPlaneURL"`
	DefaultRegion          string  `json:"defaultRegion"`
	EnableReportingPii     bool    `json:"enableReportingPii"`
	EventAuditEnabled      bool    `json:"eventAuditEnabled"`
	UseRudderServerStorage bool    `json:"useRudderServerStorage"`
	DataRetentionPeriod    string  `json:"dataRetentionPeriod"`
	Environment            string  `json:"environment"`
	IsDefault              bool    `json:"isDefault"`
	Status                 string  `json:"status"`
	DeploymentStatus       *string `json:"deploymentStatus"`
	CreatedAt              string  `json:"createdAt"`
	AdminEmail             string  `json:"adminEmail"`
	PlanID                 string  `json:"planId"`
}

type SearchBy string

const (
	SearchByWorkspaceID     SearchBy = "id"
	SearchByNamespace       SearchBy = "namespace"
	SearchByEmail           SearchBy = "email"
	SearchByOrgID           SearchBy = "organizationId"
	SearchBySourceID        SearchBy = "sourceId"
	SearchBySourceType      SearchBy = "sourceType"
	SearchByDestinationID   SearchBy = "destinationId"
	SearchByDestinationType SearchBy = "destinationType"
)
