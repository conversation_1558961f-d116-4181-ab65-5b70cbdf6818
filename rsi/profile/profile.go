package profile

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v2"
)

const (
	Default = "default"
)

// AdminAuth contains refresh token for admin authentication
type AdminAuth struct {
	RefreshToken string `yaml:"refresh_token,omitempty"`
}

type Profile struct {
	Token       string `yaml:"token"`
	WorkspaceID string `yaml:"workspace_id"`

	AdminRefreshToken string `yaml:"admin_refresh_token"`
}

type ProfileManager struct {
	ProfilesDir string
}

func NewProfileManager() *ProfileManager {
	homeDir, _ := os.UserHomeDir()
	return &ProfileManager{
		ProfilesDir: filepath.Join(homeDir, ".rudder", "profiles"),
	}
}

func (pm *ProfileManager) StoreProfile(name string, profile Profile) error {
	if err := os.MkdirAll(pm.ProfilesDir, 0755); err != nil {
		return fmt.Errorf("failed to create profiles directory: %w", err)
	}

	data, err := yaml.Marshal(profile)
	if err != nil {
		return fmt.Errorf("failed to marshal profile: %w", err)
	}

	filename := filepath.Join(pm.ProfilesDir, name+".yaml")
	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("failed to write profile file: %w", err)
	}

	return nil
}

func (pm *ProfileManager) GetProfile(name string) (Profile, error) {
	filename := filepath.Join(pm.ProfilesDir, name+".yaml")
	data, err := os.ReadFile(filename)
	if err != nil {
		return Profile{}, fmt.Errorf("failed to read profile file: %w", err)
	}

	var profile Profile
	if err := yaml.Unmarshal(data, &profile); err != nil {
		return Profile{}, fmt.Errorf("failed to unmarshal profile: %w", err)
	}

	return profile, nil
}
