name: build


on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - 'rsi/**'
      - '.github/workflows/rsi-build.yml'

concurrency: ${{ github.workflow }}-${{ github.ref }}

permissions:
  contents: write
  packages: write

jobs:
  build:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: rsi
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - run: git fetch --force --tags

      - uses: actions/setup-go@v5
        with:
          go-version-file: rsi/go.mod
          cache: true
          cache-dependency-path: rsi/go.sum

      - run: go mod download

      - uses: goreleaser/goreleaser-action@v6
        with:
          distribution: goreleaser
          version: latest
          args: build --snapshot --clean
          workdir: rsi