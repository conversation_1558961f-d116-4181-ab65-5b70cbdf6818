import { authHeader1, secret1 } from '../maskedSecrets';
export const data = [
  {
    name: 'pinterest_tag',
    description: 'Test 0',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'ABC Searched',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: ' <EMAIL>',
                  phone: '+********** ',
                  gender: 'non-binary',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                  partnerId: '12345678',
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
                network: {
                  carrier: 'VI',
                  wifi: true,
                },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                contentBrand: 'LV',
                partnerName: 'testPartner',
                contentName: 'testContent',
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                optOutType: 'LDP',
                clickId: 4567576,
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                tagId: '*********',
                advertiserId: '*********',
                appId: '*********',
                enhancedMatch: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                sendingUnHashedData: true,
                sendAsTestEvent: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              body: {
                FORM: {},
                JSON: {
                  action_source: 'web',
                  advertiser_id: '*********',
                  app_id: '*********',
                  custom_data: {
                    content_brand: 'LV',
                    content_ids: ['507f1f77bcf86cd799439011', '505bd76785ebb509fc183733'],
                    content_name: 'testContent',
                    contents: [
                      {
                        id: '507f1f77bcf86cd799439011',
                        item_category: 'Games',
                        item_name: 'Monopoly: 3rd Edition',
                        item_price: '19',
                        quantity: 1,
                      },
                      {
                        id: '505bd76785ebb509fc183733',
                        item_category: 'Games',
                        item_name: 'Uno Card Game',
                        item_price: '3',
                        quantity: 2,
                      },
                    ],
                    currency: 'USD',
                    num_items: 3,
                    opt_out_type: 'LDP',
                    order_id: '50314b8e9bcf000000000000',
                    value: '27.5',
                  },
                  device_carrier: 'VI',
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  event_name: 'watch_video',
                  event_time: **********,
                  partner_name: 'testPartner',
                  user_data: {
                    click_id: '4567576',
                    client_user_agent: 'chrome',
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ge: ['1b16b1df538ba12dc3f97edbb85caa7050d46c148134290feba80f8236c83db9'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    partner_id: '12345678',
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                  },
                  wifi: true,
                },
                JSON_ARRAY: {},
                XML: {},
              },
              endpoint: 'https://ct.pinterest.com/events/v3',
              files: {},
              headers: {
                'Content-Type': 'application/json',
              },
              method: 'POST',
              params: {
                test: true,
              },
              type: 'REST',
              userId: '',
              version: '1',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 1',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'Order completed',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                clickId: 'dummy_clickId',
                products: [
                  {
                    sku: 4579032,
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                  },
                  {
                    sku: 4649332,
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '*********',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  action_source: 'web',
                  event_name: 'checkout',
                  event_time: **********,
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  app_id: '*********',
                  advertiser_id: '*********',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                    click_id: 'dummy_clickId',
                  },
                  custom_data: {
                    currency: 'USD',
                    value: '27.5',
                    order_id: '50314b8e9bcf000000000000',
                    num_items: 3,
                    content_ids: ['4579032', '4649332'],
                    contents: [
                      {
                        id: '4579032',
                        item_category: 'Games',
                        item_name: 'Monopoly: 3rd Edition',
                        quantity: 1,
                        item_price: '19',
                      },
                      {
                        id: '4649332',
                        item_category: 'Games',
                        item_name: 'Uno Card Game',
                        quantity: 2,
                        item_price: '3',
                      },
                    ],
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 2',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'product added',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                product_id: 123,
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '*********',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_name: 'add_to_cart',
                  action_source: 'web',
                  event_time: **********,
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  app_id: '*********',
                  advertiser_id: '*********',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                  custom_data: {
                    currency: 'USD',
                    value: '27.5',
                    order_id: '50314b8e9bcf000000000000',
                    num_items: 2,
                    content_ids: ['123'],
                    contents: [{ id: '123', quantity: 2, item_price: '25' }],
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 3',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'Product List Filtered',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              destinationDefinition: { Config: { cdkV2Enabled: true } },
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error:
              'Advertiser Id not found. Aborting: Workflow: procWorkflow, Step: validateInput, ChildStep: undefined, OriginalError: Advertiser Id not found. Aborting',
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            statTags: {
              destType: 'PINTEREST_TAG',
              errorCategory: 'dataValidation',
              errorType: 'configuration',
              feature: 'processor',
              implementation: 'cdkV2',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 4',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'Product List Filtered',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '*********',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            error:
              'It is required at least one of em, hashed_maids or pair of client_ip_address and client_user_agent: Workflow: procWorkflow, Step: validateUserFields, ChildStep: undefined, OriginalError: It is required at least one of em, hashed_maids or pair of client_ip_address and client_user_agent',
            statTags: {
              destType: 'PINTEREST_TAG',
              errorCategory: 'dataValidation',
              errorType: 'instrumentation',
              feature: 'processor',
              implementation: 'cdkV2',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 5',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'ABC Searched',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: 465467456790,
                requestIP: '*********',
                numOfItems: 2,
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '*********',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_name: 'watch_video',
                  event_time: **********,
                  action_source: 'web',
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  app_id: '*********',
                  advertiser_id: '*********',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                  custom_data: {
                    currency: 'USD',
                    value: '27.5',
                    num_items: 2,
                    order_id: '465467456790',
                    content_ids: ['507f1f77bcf86cd799439011', '505bd76785ebb509fc183733'],
                    contents: [
                      {
                        id: '507f1f77bcf86cd799439011',
                        item_category: 'Games',
                        item_name: 'Monopoly: 3rd Edition',
                        quantity: 1,
                        item_price: '19',
                      },
                      {
                        id: '505bd76785ebb509fc183733',
                        item_category: 'Games',
                        item_name: 'Uno Card Game',
                        quantity: 2,
                        item_price: '3',
                      },
                    ],
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 6',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'ABC Searched',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                numOfItems: 2,
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error:
              'Advertiser Id not found. Aborting: Workflow: procWorkflow, Step: validateInput, ChildStep: undefined, OriginalError: Advertiser Id not found. Aborting',
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            statTags: {
              destType: 'PINTEREST_TAG',
              errorCategory: 'dataValidation',
              errorType: 'configuration',
              feature: 'processor',
              implementation: 'cdkV2',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 7',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'group',
              event: 'ABC Searched',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                numOfItems: 2,
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                enhancedMatch: true,
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error:
              'message type group is not supported: Workflow: procWorkflow, Step: validateInput, ChildStep: undefined, OriginalError: message type group is not supported',
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            statTags: {
              destType: 'PINTEREST_TAG',
              errorCategory: 'dataValidation',
              errorType: 'instrumentation',
              feature: 'processor',
              implementation: 'cdkV2',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 8',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'ABC Searched',
              channel: 'abc',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                numOfItems: 2,
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            error:
              'Action source must be one of app_android, app_ios, web, offline: Workflow: procWorkflow, Step: validateCommonFields, ChildStep: undefined, OriginalError: Action source must be one of app_android, app_ios, web, offline',
            statTags: {
              destType: 'PINTEREST_TAG',
              errorCategory: 'dataValidation',
              errorType: 'instrumentation',
              feature: 'processor',
              implementation: 'cdkV2',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 9',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'custom event',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                numOfItems: 2,
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                sendAsCustomEvent: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_name: 'custom',
                  event_time: **********,
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  app_id: '*********',
                  action_source: 'web',
                  advertiser_id: '123456',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                  custom_data: {
                    currency: 'USD',
                    value: '27.5',
                    num_items: 2,
                    order_id: '50314b8e9bcf000000000000',
                    content_ids: ['507f1f77bcf86cd799439011', '505bd76785ebb509fc183733'],
                    contents: [
                      {
                        id: '507f1f77bcf86cd799439011',
                        item_category: 'Games',
                        item_name: 'Monopoly: 3rd Edition',
                        quantity: 1,
                        item_price: '19',
                      },
                      {
                        id: '505bd76785ebb509fc183733',
                        item_category: 'Games',
                        item_name: 'Uno Card Game',
                        quantity: 2,
                        item_price: '3',
                      },
                    ],
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 10',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'page',
              name: 'ApplicationLoaded',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: { path: '', referrer: '', search: '', title: '', url: '' },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_time: **********,
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  action_source: 'web',
                  event_name: 'page_visit',
                  app_id: '*********',
                  advertiser_id: '123456',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 11',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'page',
              name: 'ApplicationLoaded',
              category: 'test category',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: { path: '', referrer: '', search: '', title: '', url: '' },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_time: **********,
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  action_source: 'web',
                  event_name: 'view_category',
                  app_id: '*********',
                  advertiser_id: '123456',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 12',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'page',
              name: 'ApplicationLoaded',
              category: 'test category',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123', adTrackingEnabled: true },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: { path: '', referrer: '', search: '', title: '', url: '' },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_time: **********,
                  opt_out: false,
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  action_source: 'web',
                  event_name: 'view_category',
                  app_id: '*********',
                  advertiser_id: '123456',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 13',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'page',
              name: 'ApplicationLoaded',
              category: 'test category',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                ip: '*********',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { adTrackingEnabled: false },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: { path: '', referrer: '', search: '', title: '', url: '' },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_time: **********,
                  opt_out: true,
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  action_source: 'web',
                  event_name: 'view_category',
                  app_id: '*********',
                  advertiser_id: '123456',
                  user_data: {
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    client_ip_address: '*********',
                    client_user_agent: 'chrome',
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 14',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'page',
              name: 'ApplicationLoaded',
              category: 'test category',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                requestIP: '*********',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  phone: 'Hashed phone',
                  gender: 'Hashed Gender',
                  dob: 'Hashed DB',
                  lastname: 'Hashed Lastname',
                  firstName: 'Hashed FirstName',
                  address: {
                    city: 'Hashed City',
                    state: 'Hashed State',
                    zip: 'Hashed Zip',
                    country: 'Hashed country',
                  },
                },
                device: { adTrackingEnabled: false, advertisingId: 'Hashed maids' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: { path: '', referrer: '', search: '', title: '', url: '' },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: false,
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_time: **********,
                  action_source: 'web',
                  opt_out: true,
                  event_name: 'view_category',
                  app_id: '*********',
                  advertiser_id: '123456',
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  user_data: {
                    ph: ['Hashed phone'],
                    db: ['Hashed DB'],
                    ln: ['Hashed Lastname'],
                    fn: ['Hashed FirstName'],
                    ct: ['Hashed City'],
                    st: ['Hashed State'],
                    zp: ['Hashed Zip'],
                    country: ['Hashed country'],
                    hashed_maids: ['Hashed maids'],
                    ge: ['Hashed Gender'],
                    client_user_agent: 'chrome',
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 15',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'page',
              name: 'ApplicationLoaded',
              category: 'test category',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                requestIP: '*********',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  phone: ['Hashed phone', 'Hashed phone1'],
                  gender: ['Hashed Gender', 'Hashed Gender1'],
                  dob: ['Hashed DB', 'Hashed DB1'],
                  lastname: ['Hashed Lastname', 'Hashed Lastname1'],
                  firstName: ['Hashed FirstName', 'Hashed FirstName1'],
                  address: {
                    city: ['Hashed City', 'Hashed City1'],
                    state: ['Hashed State', 'Hashed State1'],
                    zip: ['Hashed Zip', 'Hashed Zip1'],
                    country: ['Hashed country', 'Hashed country1'],
                  },
                },
                device: { adTrackingEnabled: false, advertisingId: 'Hashed maids' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: { path: '', referrer: '', search: '', title: '', url: '' },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: false,
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_time: **********,
                  action_source: 'web',
                  opt_out: true,
                  event_name: 'view_category',
                  app_id: '*********',
                  advertiser_id: '123456',
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  user_data: {
                    ph: ['Hashed phone', 'Hashed phone1'],
                    db: ['Hashed DB', 'Hashed DB1'],
                    ln: ['Hashed Lastname', 'Hashed Lastname1'],
                    fn: ['Hashed FirstName', 'Hashed FirstName1'],
                    ct: ['Hashed City', 'Hashed City1'],
                    st: ['Hashed State', 'Hashed State1'],
                    zp: ['Hashed Zip', 'Hashed Zip1'],
                    country: ['Hashed country', 'Hashed country1'],
                    hashed_maids: ['Hashed maids'],
                    ge: ['Hashed Gender', 'Hashed Gender1'],
                    client_user_agent: 'chrome',
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 16',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              name: 'Test Tool',
              type: 'page',
              sentAt: '2023-02-01T00:00:00.379Z',
              userId: '',
              channel: 'web',
              context: {
                os: { name: '', version: '' },
                app: {
                  name: 'RudderLabs JavaScript SDK',
                  version: '2.22.3',
                  namespace: 'com.rudderlabs.javascript',
                },
                page: {
                  url: 'https://www.abc.com/s598907',
                  path: '/test-path/s598907',
                  title: 'Test Tool + Reviews | Rudderstack',
                  search: '',
                  tab_url: 'https://www.abc.com/s598907',
                  referrer: '$direct',
                  initial_referrer: '$direct',
                  referring_domain: '',
                  initial_referring_domain: '',
                },
                locale: 'en-US',
                screen: {
                  width: 1024,
                  height: 1024,
                  density: 1,
                  innerWidth: 1024,
                  innerHeight: 1024,
                },
                traits: {},
                library: { name: 'RudderLabs JavaScript SDK', version: '2.22.3' },
                campaign: {},
                doNotSell: false,
                sessionId: 1675209600203,
                userAgent:
                  'Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/109.0.5414.101 Safari/537.36',
                gaClientId: {
                  integrations: {
                    'Google Ads': { gclid: '' },
                    'Google Analytics': { clientId: '1518934611.1234569600' },
                  },
                },
                sessionStart: true,
              },
              rudderId: '7291a10f-e7dd-49f9-94ce-0154f53897y6',
              messageId: '1c77a616-13a7-4a2e-a8e7-e1a0971897y6',
              timestamp: '2023-02-01T12:47:30.030Z',
              properties: {
                sku: '45790-32',
                url: 'https://www.abc.com/23rty',
                name: 'Test Tool',
                path: '/test-path/tool',
                email: '',
                title: 'Test Tool + Reviews | Rudderstack',
                review: { reviewCount: 2, averageReview: 5, reviewContentID: ['238300132'] },
                search: '',
                tab_url: 'https://www.abc/com',
                pageInfo: {
                  pageId: 's592897',
                  category: {
                    pageType: 'product',
                    subCategory: 'Dining & Kitchen Furniture',
                    pageTemplate: 'product detail grouper',
                    primaryCategory: 'Furniture',
                  },
                  brandType: 'new brand',
                },
                referrer: '',
                subCategory: 'Dining & Kitchen Furniture',
                primaryCategory: 'Furniture',
                initial_referrer: '$direct',
                referring_domain: '',
                initial_referring_domain: '',
              },
              receivedAt: '2023-02-01T12:47:30.038Z',
              request_ip: '*************',
              anonymousId: 'a61c77a6-1613-474a-aee8-e7e1a0971047',
              integrations: { All: true },
              originalTimestamp: '2023-02-01T00:00:00.371Z',
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123478671210',
                sendingUnHashedData: false,
                enableDeduplication: false,
                eventsMapping: [
                  { from: 'Product Added', to: 'AddToCart' },
                  { from: 'Order Completed', to: 'Checkout' },
                  { from: 'Product Viewed', to: 'PageVisit' },
                  { from: 'Lead', to: 'Lead' },
                  { from: 'Signup', to: 'Signup' },
                ],
                enhancedMatch: true,
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              body: {
                JSON: {
                  event_time: 1675255650,
                  event_source_url: 'https://www.abc.com/s598907',
                  action_source: 'web',
                  app_name: 'RudderLabs JavaScript SDK',
                  app_version: '2.22.3',
                  language: 'en-US',
                  event_id: '1c77a616-13a7-4a2e-a8e7-e1a0971897y6',
                  advertiser_id: '123478671210',
                  user_data: {
                    client_ip_address: '*************',
                    client_user_agent:
                      'Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko; compatible; Googlebot/2.1; +http://www.google.com/bot.html) Chrome/109.0.5414.101 Safari/537.36',
                  },
                  event_name: 'page_visit',
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 17',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'test',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                sku: '1234',
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                sendAsCustomEvent: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_name: 'custom',
                  event_time: **********,
                  action_source: 'web',
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  app_id: '*********',
                  advertiser_id: '123456',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                  custom_data: {
                    currency: 'USD',
                    value: '27.5',
                    order_id: '50314b8e9bcf000000000000',
                    num_items: 0,
                    content_ids: ['1234'],
                    contents: [{ id: '1234', quantity: 1 }],
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 18',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'custom event',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                sku: '1234',
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                ],
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                sendAsCustomEvent: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://ct.pinterest.com/events/v3',
              headers: { 'Content-Type': 'application/json' },
              params: {},
              body: {
                JSON: {
                  event_name: 'custom',
                  event_time: **********,
                  action_source: 'web',
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  app_id: '*********',
                  advertiser_id: '123456',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                  custom_data: {
                    currency: 'USD',
                    value: '27.5',
                    order_id: '50314b8e9bcf000000000000',
                    num_items: 1,
                    content_ids: ['507f1f77bcf86cd799439011'],
                    contents: [
                      {
                        id: '507f1f77bcf86cd799439011',
                        item_category: 'Games',
                        item_name: 'Monopoly: 3rd Edition',
                        quantity: 1,
                        item_price: '19',
                      },
                    ],
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 19',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'custom event',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                advertiserId: '123456',
                appId: '*********',
                sendingUnHashedData: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                enhancedMatch: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              body: {
                FORM: {},
                JSON: {
                  action_source: 'web',
                  advertiser_id: '123456',
                  app_id: '*********',
                  custom_data: {
                    contents: [{ quantity: 1 }],
                    currency: 'USD',
                    num_items: 0,
                    order_id: '50314b8e9bcf000000000000',
                    value: '27.5',
                  },
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  event_name: 'custom event',
                  event_time: **********,
                  user_data: {
                    client_user_agent: 'chrome',
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                  },
                },
                JSON_ARRAY: {},
                XML: {},
              },
              endpoint: 'https://ct.pinterest.com/events/v3',
              files: {},
              headers: { 'Content-Type': 'application/json' },
              method: 'POST',
              params: {},
              type: 'REST',
              version: '1',
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 20',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            description: 'Track call with v5 Api version and send external_id toggle enabled',
            message: {
              type: 'track',
              event: 'ABC Searched',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                optOutType: 'LDP',
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                apiVersion: 'newApi',
                adAccountId: 'accountId123',
                conversionToken: secret1,
                appId: '*********',
                enhancedMatch: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                sendingUnHashedData: true,
                sendExternalId: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://api.pinterest.com/v5/ad_accounts/accountId123/events',
              headers: {
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              params: {},
              body: {
                JSON: {
                  action_source: 'web',
                  event_name: 'watch_video',
                  event_time: **********,
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  app_id: '*********',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                    external_id: [
                      '3217d71a74c219d6e31e28267b313a7ceb6a2c032db1a091c9416b25b2ae2bc8',
                    ],
                  },
                  custom_data: {
                    currency: 'USD',
                    value: '27.5',
                    order_id: '50314b8e9bcf000000000000',
                    opt_out_type: 'LDP',
                    num_items: 3,
                    content_ids: ['507f1f77bcf86cd799439011', '505bd76785ebb509fc183733'],
                    contents: [
                      {
                        id: '507f1f77bcf86cd799439011',
                        item_category: 'Games',
                        item_name: 'Monopoly: 3rd Edition',
                        quantity: 1,
                        item_price: '19',
                      },
                      {
                        id: '505bd76785ebb509fc183733',
                        item_category: 'Games',
                        item_name: 'Uno Card Game',
                        quantity: 2,
                        item_price: '3',
                      },
                    ],
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 21',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            description: 'Custom event with v5 Api version',
            message: {
              type: 'track',
              event: 'random',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                optOutType: 'LDP',
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: 56457,
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                apiVersion: 'newApi',
                adAccountId: 'accountId123',
                conversionToken: secret1,
                appId: '*********',
                enhancedMatch: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                sendingUnHashedData: true,
                sendAsCustomEvent: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://api.pinterest.com/v5/ad_accounts/accountId123/events',
              headers: {
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              params: {},
              body: {
                JSON: {
                  action_source: 'web',
                  event_name: 'custom',
                  event_time: **********,
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  app_id: '*********',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                  custom_data: {
                    currency: 'USD',
                    value: '27.5',
                    order_id: '50314b8e9bcf000000000000',
                    opt_out_type: 'LDP',
                    num_items: 3,
                    content_ids: ['56457', '505bd76785ebb509fc183733'],
                    contents: [
                      {
                        id: '56457',
                        item_category: 'Games',
                        item_name: 'Monopoly: 3rd Edition',
                        quantity: 1,
                        item_price: '19',
                      },
                      {
                        id: '505bd76785ebb509fc183733',
                        item_category: 'Games',
                        item_name: 'Uno Card Game',
                        quantity: 2,
                        item_price: '3',
                      },
                    ],
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 22',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            description:
              'Custom event with v5 Api version, with unhashed User Data and the values are an of strings',
            message: {
              type: 'track',
              event: 'random',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: ['<EMAIL>', '<EMAIL>'],
                  phone: ['+**********', '+1234589948'],
                  ge: ['male', 'male'],
                  db: ['********', '19970615'],
                  lastname: ['Rudderlabs', 'Xu'],
                  firstName: ['Test', 'Alex'],
                  address: {
                    city: ['Kolkata', 'Mumbai'],
                    state: ['WB', 'MH'],
                    zip: ['700114', '700115'],
                    country: ['IN', 'IN'],
                  },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                optOutType: 'LDP',
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                apiVersion: 'newApi',
                adAccountId: 'accountId123',
                conversionToken: secret1,
                appId: '*********',
                enhancedMatch: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                sendingUnHashedData: true,
                sendAsCustomEvent: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://api.pinterest.com/v5/ad_accounts/accountId123/events',
              headers: {
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              params: {},
              body: {
                JSON: {
                  action_source: 'web',
                  event_name: 'custom',
                  event_time: **********,
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  app_id: '*********',
                  user_data: {
                    em: [
                      '48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08',
                      'c392e50ebeca7bea4405e9c545023451ac56620031f81263f681269bde14218b',
                    ],
                    ph: [
                      'd164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b',
                      '22bdde2594851294f2a6f4c34af704e68b398b03129ea9ceb58f0ffe33f6db52',
                    ],
                    ln: [
                      'dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251',
                      '9c2f138690fca4890c3c4a6691610fbbbdf32091cc001f7355cfdf574baa52b9',
                    ],
                    fn: [
                      '9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08',
                      '4135aa9dc1b842a653dea846903ddb95bfb8c5a10c504a7fa16e10bc31d1fdf0',
                    ],
                    ct: [
                      '6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85',
                      'd209bcc17778fd19fd2bc0c99a3868bf011da5162d3a75037a605768ebc276e2',
                    ],
                    st: [
                      '3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd',
                      '1b0316ed1cfed044035c55363e02ccafab26d66b1c2746b94d17285f043324aa',
                    ],
                    zp: [
                      '1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c',
                      '4d6755aa1e85517191f06cc91448696c173e1195ae51f94a1670116ac7b5c47b',
                    ],
                    country: [
                      '582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf',
                      '582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf',
                    ],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                  custom_data: {
                    currency: 'USD',
                    value: '27.5',
                    order_id: '50314b8e9bcf000000000000',
                    opt_out_type: 'LDP',
                    num_items: 3,
                    content_ids: ['507f1f77bcf86cd799439011', '505bd76785ebb509fc183733'],
                    contents: [
                      {
                        id: '507f1f77bcf86cd799439011',
                        item_category: 'Games',
                        item_name: 'Monopoly: 3rd Edition',
                        quantity: 1,
                        item_price: '19',
                      },
                      {
                        id: '505bd76785ebb509fc183733',
                        item_category: 'Games',
                        item_name: 'Uno Card Game',
                        quantity: 2,
                        item_price: '3',
                      },
                    ],
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 23',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            description: 'Ad Account Id check in V5',
            message: {
              type: 'track',
              event: 'random',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                apiVersion: 'newApi',
                adAccountId: '',
                conversionToken: secret1,
                appId: '*********',
                enhancedMatch: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                sendingUnHashedData: true,
                sendAsCustomEvent: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error:
              'Ad Account ID not found. Aborting: Workflow: procWorkflow, Step: validateInput, ChildStep: undefined, OriginalError: Ad Account ID not found. Aborting',
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            statTags: {
              destType: 'PINTEREST_TAG',
              errorCategory: 'dataValidation',
              errorType: 'configuration',
              feature: 'processor',
              implementation: 'cdkV2',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 24',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            description: 'Conversion Token check in V5',
            message: {
              type: 'track',
              event: 'random',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                apiVersion: 'newApi',
                adAccountId: 'accountId123',
                conversionToken: '',
                appId: '*********',
                enhancedMatch: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                sendingUnHashedData: true,
                sendAsCustomEvent: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error:
              'Conversion Token not found. Aborting: Workflow: procWorkflow, Step: validateInput, ChildStep: undefined, OriginalError: Conversion Token not found. Aborting',
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            statTags: {
              destType: 'PINTEREST_TAG',
              errorCategory: 'dataValidation',
              errorType: 'configuration',
              feature: 'processor',
              implementation: 'cdkV2',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 25',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'custom event',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: [27.5, 123],
                coupon: 'hasbros',
                revenue: 48,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                apiVersion: 'newApi',
                adAccountId: 'accountId123',
                conversionToken: secret1,
                appId: '*********',
                enhancedMatch: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                sendingUnHashedData: true,
                sendAsCustomEvent: false,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              body: {
                JSON: {
                  event_time: **********,
                  action_source: 'web',
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  app_id: '*********',
                  user_data: {
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    client_user_agent: 'chrome',
                  },
                  custom_data: {
                    num_items: 0,
                    contents: [
                      {
                        quantity: 1,
                      },
                    ],
                    currency: 'USD',
                    value: '[27.5,123]',
                    order_id: '50314b8e9bcf000000000000',
                  },
                  event_name: 'custom event',
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: 'https://api.pinterest.com/v5/ad_accounts/accountId123/events',
              headers: {
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              params: {},
              files: {},
              userId: '',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 26',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              channel: 'web',
              sentAt: '2020-08-14T05:30:30.118Z',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                apiVersion: 'newApi',
                adAccountId: 'accountId123',
                conversionToken: secret1,
                appId: '*********',
                enhancedMatch: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                sendingUnHashedData: true,
                sendAsCustomEvent: false,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error:
              'event_name could not be mapped. Aborting.: Workflow: procWorkflow, Step: validateInputForTrack, ChildStep: undefined, OriginalError: event_name could not be mapped. Aborting.',
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            statTags: {
              destType: 'PINTEREST_TAG',
              errorCategory: 'dataValidation',
              errorType: 'instrumentation',
              feature: 'processor',
              implementation: 'cdkV2',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 3',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            description: 'Any other format of event except string should be aborted',
            message: {
              type: 'track',
              event: [1, 2, 3],
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: '<EMAIL>',
                  phone: '+**********',
                  ge: 'male',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: '50314b8e9bcf000000000000',
                requestIP: '*********',
                optOutType: 'LDP',
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: '507f1f77bcf86cd799439011',
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                sendAsTestEvent: false,
                tagId: '*********',
                apiVersion: 'newApi',
                adAccountId: 'accountId123',
                conversionToken: secret1,
                appId: '*********',
                enhancedMatch: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                sendingUnHashedData: true,
                sendExternalId: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error:
              'Event is a required field and should be a string: Workflow: procWorkflow, Step: eventNames, ChildStep: undefined, OriginalError: Event is a required field and should be a string',
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            statTags: {
              destType: 'PINTEREST_TAG',
              errorCategory: 'dataValidation',
              errorType: 'instrumentation',
              feature: 'processor',
              implementation: 'cdkV2',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'pinterest_tag',
    description: 'Test 4: send clickId, orderId as array and object respectively',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'track',
              event: 'ABC Searched',
              sentAt: '2020-08-14T05:30:30.118Z',
              channel: 'web',
              context: {
                source: 'test',
                userAgent: 'chrome',
                traits: {
                  anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
                  email: ' <EMAIL>',
                  phone: '+********** ',
                  gender: 'non-binary',
                  db: '********',
                  lastname: 'Rudderlabs',
                  firstName: 'Test',
                  address: { city: 'Kolkata', state: 'WB', zip: '700114', country: 'IN' },
                  partnerId: '12345678',
                },
                device: { advertisingId: 'abc123' },
                library: { name: 'rudder-sdk-ruby-sync', version: '1.0.6' },
                network: {
                  carrier: 'VI',
                  wifi: true,
                },
              },
              messageId: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
              timestamp: '2020-08-14T05:30:30.118Z',
              properties: {
                contentBrand: 'LV',
                partnerName: 'testPartner',
                contentName: 'testContent',
                tax: 2,
                total: 27.5,
                coupon: 'hasbros',
                revenue: 48,
                price: 25,
                quantity: 2,
                currency: 'USD',
                discount: 2.5,
                order_id: { a: 'b' },
                requestIP: '*********',
                optOutType: 'LDP',
                clickId: ['hj', 'tiui'],
                products: [
                  {
                    sku: '45790-32',
                    url: 'https://www.example.com/product/path',
                    name: 'Monopoly: 3rd Edition',
                    price: 19,
                    category: 'Games',
                    quantity: 1,
                    image_url: 'https:///www.example.com/product/path.jpg',
                    product_id: 3456,
                  },
                  {
                    sku: '46493-32',
                    name: 'Uno Card Game',
                    price: 3,
                    category: 'Games',
                    quantity: 2,
                    product_id: '505bd76785ebb509fc183733',
                  },
                ],
                shipping: 3,
                subtotal: 22.5,
                affiliation: 'Google Store',
                checkout_id: 'fksdjfsdjfisjf9sdfjsd9f',
              },
              anonymousId: '50be5c78-6c3f-4b60-be84-97805a316fb1',
              integrations: { All: true },
            },
            destination: {
              DestinationDefinition: { Config: { cdkV2Enabled: true } },
              ID: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq',
              Name: 'PINTEREST_TAG',
              Config: {
                tagId: '*********',
                advertiserId: '*********',
                appId: '*********',
                enhancedMatch: true,
                enableDeduplication: true,
                deduplicationKey: 'messageId',
                sendingUnHashedData: true,
                sendAsTestEvent: true,
                customProperties: [{ properties: 'presentclass' }, { properties: 'presentgrade' }],
                eventsMapping: [{ from: 'ABC Searched', to: 'WatchVideo' }],
              },
              Enabled: true,
              Transformations: [],
            },
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            metadata: { destintionId: '1pYpzzvcn7AQ2W9GGIAZSsN6Mfq' },
            output: {
              body: {
                FORM: {},
                JSON: {
                  action_source: 'web',
                  advertiser_id: '*********',
                  app_id: '*********',
                  custom_data: {
                    content_brand: 'LV',
                    content_ids: ['3456', '505bd76785ebb509fc183733'],
                    content_name: 'testContent',
                    contents: [
                      {
                        id: '3456',
                        item_category: 'Games',
                        item_name: 'Monopoly: 3rd Edition',
                        item_price: '19',
                        quantity: 1,
                      },
                      {
                        id: '505bd76785ebb509fc183733',
                        item_category: 'Games',
                        item_name: 'Uno Card Game',
                        item_price: '3',
                        quantity: 2,
                      },
                    ],
                    currency: 'USD',
                    num_items: 3,
                    opt_out_type: 'LDP',
                    order_id: '{"a":"b"}',
                    value: '27.5',
                  },
                  device_carrier: 'VI',
                  event_id: '7208bbb6-2c4e-45bb-bf5b-ad426f3593e9',
                  event_name: 'watch_video',
                  event_time: **********,
                  partner_name: 'testPartner',
                  user_data: {
                    click_id: '["hj","tiui"]',
                    client_user_agent: 'chrome',
                    country: ['582967534d0f909d196b97f9e6921342777aea87b46fa52df165389db1fb8ccf'],
                    ct: ['6689106ca7922c30b2fd2c175c85bc7fc2d52cc4941bdd7bb622c6cdc6284a85'],
                    em: ['48ddb93f0b30c475423fe177832912c5bcdce3cc72872f8051627967ef278e08'],
                    fn: ['9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08'],
                    ge: ['1b16b1df538ba12dc3f97edbb85caa7050d46c148134290feba80f8236c83db9'],
                    hashed_maids: [
                      '6ca13d52ca70c883e0f0bb101e425a89e8624de51db2d2392593af6a84118090',
                    ],
                    ln: ['dcf000c2386fb76d22cefc0d118a8511bb75999019cd373df52044bccd1bd251'],
                    partner_id: '12345678',
                    ph: ['d164bbe036663cb5c96835e9ccc6501e9a521127ea62f6359744928ba932413b'],
                    st: ['3b45022ab36728cdae12e709e945bba267c50ee8a91e6e4388539a8e03a3fdcd'],
                    zp: ['1a4292e00780e18d00e76fde9850aee5344e939ba593333cd5e4b4aa2cd33b0c'],
                  },
                  wifi: true,
                },
                JSON_ARRAY: {},
                XML: {},
              },
              endpoint: 'https://ct.pinterest.com/events/v3',
              files: {},
              headers: {
                'Content-Type': 'application/json',
              },
              method: 'POST',
              params: {
                test: true,
              },
              type: 'REST',
              userId: '',
              version: '1',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
];
