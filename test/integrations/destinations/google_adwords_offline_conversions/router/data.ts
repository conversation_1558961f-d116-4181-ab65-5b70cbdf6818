import { authHeader1, secret1, secret3 } from '../maskedSecrets';
import { timestampMock } from '../mocks';

const API_VERSION = 'v18';

export const data = [
  {
    name: 'google_adwords_offline_conversions',
    description: 'Test 0',
    feature: 'router',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: {
          input: [
            {
              message: {
                channel: 'web',
                context: {
                  app: {
                    build: '1.0.0',
                    name: '<PERSON><PERSON><PERSON>abs JavaScript SDK',
                    namespace: 'com.rudderlabs.javascript',
                    version: '1.0.0',
                  },
                  device: {
                    id: '0572f78fa49c648e',
                    name: 'generic_x86_arm',
                    type: 'Android',
                    model: 'AOSP on IA Emulator',
                    manufacturer: 'Google',
                    adTrackingEnabled: true,
                    advertisingId: '44c97318-9040-4361-8bc7-4eb30f665ca8',
                  },
                  traits: {
                    email: '<EMAIL>',
                    phone: '******-555-0146',
                    firstName: 'John',
                    lastName: 'Gomes',
                    city: 'London',
                    state: 'England',
                    countryCode: 'GB',
                    postalCode: 'EC3M',
                    streetAddress: '71 Cherry Court SOUTHAMPTON SO53 5PD UK',
                  },
                  library: { name: 'RudderLabs JavaScript SDK', version: '1.0.0' },
                  userAgent:
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36',
                  locale: 'en-US',
                  ip: '0.0.0.0',
                  os: { name: '', version: '' },
                  screen: { density: 2 },
                },
                event: 'Promotion Clicked',
                type: 'track',
                messageId: '5e10d13a-bf9a-44bf-b884-43a9e591ea71',
                originalTimestamp: '2019-10-14T11:15:18.299Z',
                anonymousId: '00000000000000000000000000',
                userId: '12345',
                properties: {
                  gbraid: 'gbraid',
                  wbraid: 'wbraid',
                  externalAttributionCredit: 10,
                  externalAttributionModel: 'externalAttributionModel',
                  conversionCustomVariable: 'conversionCustomVariable',
                  value: 'value',
                  merchantId: '9876merchantId',
                  feedCountryCode: 'feedCountryCode',
                  feedLanguageCode: 'feedLanguageCode',
                  localTransactionCost: 20,
                  products: [
                    {
                      product_id: '507f1f77bcf86cd799439011',
                      quantity: '2',
                      price: '50',
                      sku: '45790-32',
                      name: 'Monopoly: 3rd Edition',
                      position: '1',
                      category: 'cars',
                      url: 'https://www.example.com/product/path',
                      image_url: 'https://www.example.com/product/path.jpg',
                    },
                  ],
                  userIdentifierSource: 'FIRST_PARTY',
                  conversionEnvironment: 'WEB',
                  gclid: 'gclid',
                  conversionDateTime: '2022-01-01 12:32:45-08:00',
                  conversionValue: '1',
                  currency: 'GBP',
                },
                integrations: { All: true },
                name: 'ApplicationLoaded',
                sentAt: '2019-10-14T11:15:53.296Z',
              },
              metadata: {
                secret: {
                  access_token: secret1,
                  refresh_token: 'efgh5678',
                  developer_token: secret3,
                },
                jobId: 1,
                userId: 'u1',
              },
              destination: {
                Config: {
                  customerId: '962-581-2972',
                  eventsToOfflineConversionsTypeMapping: [
                    { from: 'Sign up completed', to: 'click' },
                    { from: 'Download', to: 'call' },
                    { from: 'Promotion Clicked', to: 'click' },
                    { from: 'Product Searched', to: 'call' },
                  ],
                  eventsToConversionsNamesMapping: [
                    { from: 'Sign up completed', to: 'Sign-up - click' },
                    { from: 'Download', to: 'Page view' },
                    { from: 'Promotion Clicked', to: 'Sign-up - click' },
                    { from: 'Product Searched', to: 'search' },
                  ],
                  customVariables: [
                    { from: 'value', to: 'revenue' },
                    { from: 'total', to: 'cost' },
                  ],
                  UserIdentifierSource: 'THIRD_PARTY',
                  conversionEnvironment: 'WEB',
                  hashUserIdentifier: true,
                  defaultUserIdentifier: 'email',
                  validateOnly: false,
                  rudderAccountId: '25u5whFH7gVTnCiAjn4ykoCLGoC',
                },
              },
            },
            {
              message: {
                channel: 'web',
                context: {
                  app: {
                    build: '1.0.0',
                    name: 'RudderLabs JavaScript SDK',
                    namespace: 'com.rudderlabs.javascript',
                    version: '1.0.0',
                  },
                  device: {
                    id: '0572f78fa49c648e',
                    name: 'generic_x86_arm',
                    type: 'Android',
                    model: 'AOSP on IA Emulator',
                    manufacturer: 'Google',
                    adTrackingEnabled: true,
                    advertisingId: '44c97318-9040-4361-8bc7-4eb30f665ca8',
                  },
                  traits: {
                    email: '<EMAIL>',
                    phone: '******-555-0146',
                    firstName: 'John',
                    lastName: 'Gomes',
                    city: 'London',
                    state: 'England',
                    countryCode: 'GB',
                    postalCode: 'EC3M',
                    streetAddress: '71 Cherry Court SOUTHAMPTON SO53 5PD UK',
                  },
                  library: { name: 'RudderLabs JavaScript SDK', version: '1.0.0' },
                  userAgent:
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.90 Safari/537.36',
                  locale: 'en-US',
                  ip: '0.0.0.0',
                  os: { name: '', version: '' },
                  screen: { density: 2 },
                },
                event: 'Product Searched',
                type: 'track',
                messageId: '5e10d13a-bf9a-44bf-b884-43a9e591ea71',
                originalTimestamp: '2019-10-14T11:15:18.299Z',
                anonymousId: '00000000000000000000000000',
                userId: '12345',
                properties: {
                  externalAttributionCredit: 10,
                  externalAttributionModel: 'externalAttributionModel',
                  merchantId: 'merchantId',
                  feedCountryCode: 'feedCountryCode',
                  feedLanguageCode: 'feedLanguageCode',
                  localTransactionCost: 20,
                  products: [
                    {
                      product_id: '507f1f77bcf86cd799439011',
                      quantity: '2',
                      price: '50',
                      sku: '45790-32',
                      name: 'Monopoly: 3rd Edition',
                      position: '1',
                      category: 'cars',
                      url: 'https://www.example.com/product/path',
                      image_url: 'https://www.example.com/product/path.jpg',
                    },
                  ],
                  userIdentifierSource: 'FIRST_PARTY',
                  conversionEnvironment: 'WEB',
                  gclid: 'gclid',
                  conversionCustomVariable: 'conversionCustomVariable',
                  value: 'value',
                  callerId: 'callerId',
                  callStartDateTime: '2022-08-28 15:01:30+05:30',
                  conversionDateTime: '2022-01-01 12:32:45-08:00',
                  conversionValue: '1',
                  currency: 'GBP',
                },
                integrations: { All: true },
                name: 'ApplicationLoaded',
                sentAt: '2019-10-14T11:15:53.296Z',
              },
              metadata: {
                secret: {
                  access_token: secret1,
                  refresh_token: 'efgh5678',
                  developer_token: secret3,
                },
                jobId: 2,
                userId: 'u1',
              },
              destination: {
                Config: {
                  customerId: '962-581-2972',
                  eventsToOfflineConversionsTypeMapping: [
                    { from: 'Sign up completed', to: 'click' },
                    { from: 'Download', to: 'call' },
                    { from: 'Promotion Clicked', to: 'click' },
                    { from: 'Product Searched', to: 'call' },
                  ],
                  eventsToConversionsNamesMapping: [
                    { from: 'Sign up completed', to: 'Sign-up - click' },
                    { from: 'Download', to: 'Page view' },
                    { from: 'Promotion Clicked', to: 'Sign-up - click' },
                    { from: 'Product Searched', to: 'search' },
                  ],
                  customVariables: [
                    { from: 'value', to: 'revenue' },
                    { from: 'total', to: 'cost' },
                  ],
                  UserIdentifierSource: 'THIRD_PARTY',
                  conversionEnvironment: 'WEB',
                  hashUserIdentifier: true,
                  defaultUserIdentifier: 'phone',
                  validateOnly: false,
                  rudderAccountId: '25u5whFH7gVTnCiAjn4ykoCLGoC',
                },
              },
            },
            {
              message: {
                channel: 'web',
                context: { traits: { firstName: 'John' } },
                event: 'Product Clicked',
                type: 'track',
                messageId: '5e10d13a-bf9a-44bf-b884-43a9e591ea71',
                originalTimestamp: '2019-10-14T11:15:18.299Z',
                anonymousId: '00000000000000000000000000',
                userId: '12345',
                properties: {
                  loyaltyFraction: 1,
                  order_id: 'order id',
                  currency: 'INR',
                  revenue: '100',
                  store_code: 'store code',
                  email: '<EMAIL>',
                  gclid: 'gclid',
                  product_id: '123445',
                  quantity: 123,
                  callerId: '1234',
                  callStartDateTime: '2019-10-14T11:15:18.299Z',
                },
                integrations: { All: true },
                name: 'ApplicationLoaded',
                sentAt: '2019-10-14T11:15:53.296Z',
              },
              metadata: {
                secret: {
                  access_token: secret1,
                  refresh_token: 'efgh5678',
                  developer_token: secret3,
                },
                jobId: 3,
                userId: 'u1',
              },
              destination: {
                Config: {
                  rudderAccountId: '2Hsy2iFyoG5VLDd9wQcggHLMYFA',
                  customerId: '769-372-9833',
                  subAccount: false,
                  UserIdentifierSource: 'FIRST_PARTY',
                  conversionEnvironment: 'none',
                  defaultUserIdentifier: 'email',
                  hashUserIdentifier: true,
                  validateOnly: true,
                  eventsToOfflineConversionsTypeMapping: [
                    { from: 'Data Reading Guide', to: 'click' },
                    { from: 'Order Completed', to: 'store' },
                    { from: 'Sign-up - click', to: 'click' },
                    { from: 'Outbound click (rudderstack.com)', to: 'click' },
                    { from: 'Page view', to: 'click' },
                    { from: 'download', to: 'click' },
                    { from: 'Product Clicked', to: 'store' },
                    { from: 'Order Completed', to: 'call' },
                  ],
                  loginCustomerId: '**********',
                  eventsToConversionsNamesMapping: [
                    { from: 'Data Reading Guide', to: 'Data Reading Guide' },
                    { from: 'Order Completed', to: 'Order Completed' },
                    { from: 'Sign-up - click', to: 'Sign-up - click' },
                    {
                      from: 'Outbound click (rudderstack.com)',
                      to: 'Outbound click (rudderstack.com)',
                    },
                    { from: 'Page view', to: 'Page view' },
                    { from: 'Sign up completed', to: 'Sign-up - click' },
                    { from: 'download', to: 'Page view' },
                    { from: 'Product Clicked', to: 'Store sales' },
                  ],
                  authStatus: 'active',
                  customVariables: [{ from: '', to: '' }],
                },
              },
            },
            {
              message: {
                channel: 'web',
                context: { traits: { firstName: 'John' } },
                event: 'Order Completed',
                type: 'track',
                messageId: '5e10d13a-bf9a-44bf-b884-43a9e591ea71',
                originalTimestamp: '2019-10-14T11:15:18.299Z',
                anonymousId: '00000000000000000000000000',
                userId: '12345',
                properties: {
                  loyaltyFraction: 1,
                  order_id: 'order id',
                  currency: 'INR',
                  revenue: '100',
                  store_code: 'store code2',
                  email: '<EMAIL>',
                  gclid: 'gclid',
                  product_id: '123445',
                  quantity: 123,
                  callerId: '1234',
                  callStartDateTime: '2019-10-14T11:15:18.299Z',
                },
                integrations: { All: true },
                name: 'ApplicationLoaded',
                sentAt: '2019-10-14T11:15:53.296Z',
              },
              metadata: {
                secret: {
                  access_token: secret1,
                  refresh_token: 'efgh5678',
                  developer_token: secret3,
                },
                jobId: 4,
                userId: 'u1',
              },
              destination: {
                Config: {
                  rudderAccountId: '2Hsy2iFyoG5VLDd9wQcggHLMYFA',
                  customerId: '769-372-9833',
                  subAccount: false,
                  UserIdentifierSource: 'FIRST_PARTY',
                  conversionEnvironment: 'none',
                  defaultUserIdentifier: 'email',
                  hashUserIdentifier: true,
                  validateOnly: true,
                  eventsToOfflineConversionsTypeMapping: [
                    { from: 'Data Reading Guide', to: 'click' },
                    { from: 'Order Completed', to: 'store' },
                    { from: 'Sign-up - click', to: 'click' },
                    { from: 'Outbound click (rudderstack.com)', to: 'click' },
                    { from: 'Page view', to: 'click' },
                    { from: 'download', to: 'click' },
                    { from: 'Product Clicked', to: 'store' },
                    { from: 'Order Completed', to: 'call' },
                  ],
                  loginCustomerId: '**********',
                  eventsToConversionsNamesMapping: [
                    { from: 'Data Reading Guide', to: 'Data Reading Guide' },
                    { from: 'Order Completed', to: 'Order Completed' },
                    { from: 'Sign-up - click', to: 'Sign-up - click' },
                    {
                      from: 'Outbound click (rudderstack.com)',
                      to: 'Outbound click (rudderstack.com)',
                    },
                    { from: 'Page view', to: 'Page view' },
                    { from: 'Sign up completed', to: 'Sign-up - click' },
                    { from: 'download', to: 'Page view' },
                    { from: 'Product Clicked', to: 'Store sales' },
                  ],
                  authStatus: 'active',
                  customVariables: [{ from: '', to: '' }],
                },
              },
            },
            {
              message: {
                channel: 'web',
                context: { traits: { firstName: 'John' } },
                event: 'Order Completed',
                type: 'track',
                messageId: '5e10d13a-bf9a-44bf-b884-43a9e591ea71',
                originalTimestamp: '2019-10-14T11:15:18.299Z',
                anonymousId: '00000000000000000000000000',
                userId: '12345',
                properties: {
                  loyaltyFraction: 1,
                  order_id: 'order id',
                  currency: 'INR',
                  revenue: '100',
                  store_code: 'store code2',
                  email: '<EMAIL>',
                  gclid: 'gclid',
                  product_id: '123445',
                  quantity: 123,
                  callerId: '1234',
                  callStartDateTime: '2019-10-14T11:15:18.299Z',
                },
                integrations: { All: true },
                name: 'ApplicationLoaded',
                sentAt: '2019-10-14T11:15:53.296Z',
              },
              metadata: {
                secret: {
                  access_token: secret1,
                  refresh_token: 'efgh5678',
                  developer_token: secret3,
                },
                jobId: 5,
                userId: 'u1',
              },
              destination: {
                Config: {
                  rudderAccountId: '2Hsy2iFyoG5VLDd9wQcggHLMYFA',
                  customerId: '769-372-9833',
                  subAccount: false,
                  UserIdentifierSource: 'FIRST_PARTY',
                  conversionEnvironment: 'none',
                  defaultUserIdentifier: 'email',
                  hashUserIdentifier: true,
                  validateOnly: true,
                  eventsToOfflineConversionsTypeMapping: [
                    { from: 'Data Reading Guide', to: 'click' },
                    { from: 'Sign-up - click', to: 'click' },
                    { from: 'Outbound click (rudderstack.com)', to: 'click' },
                    { from: 'Page view', to: 'click' },
                    { from: 'download', to: 'click' },
                    { from: 'Product Clicked', to: 'store' },
                    { from: 'Order Completed', to: 'call' },
                  ],
                  loginCustomerId: '**********',
                  eventsToConversionsNamesMapping: [
                    { from: 'Data Reading Guide', to: 'Data Reading Guide' },
                    { from: 'Sign-up - click', to: 'Sign-up - click' },
                    {
                      from: 'Outbound click (rudderstack.com)',
                      to: 'Outbound click (rudderstack.com)',
                    },
                    { from: 'Page view', to: 'Page view' },
                    { from: 'Sign up completed', to: 'Sign-up - click' },
                    { from: 'download', to: 'Page view' },
                    { from: 'Product Clicked', to: 'Store sales' },
                  ],
                  authStatus: 'active',
                  customVariables: [{ from: '', to: '' }],
                },
              },
            },
          ],
          destType: 'google_adwords_offline_conversions',
        },
        method: 'POST',
      },
    },
    output: {
      response: {
        status: 200,
        body: {
          output: [
            {
              batchedRequest: [
                {
                  version: '1',
                  type: 'REST',
                  method: 'POST',
                  endpoint: `https://googleads.googleapis.com/${API_VERSION}/customers/7693729833/offlineUserDataJobs`,
                  headers: {
                    Authorization: authHeader1,
                    'Content-Type': 'application/json',
                    'developer-token': secret3,
                  },
                  params: { event: 'Store sales', customerId: '7693729833' },
                  body: {
                    JSON: {
                      event: '7693729833',
                      isStoreConversion: true,
                      createJobPayload: {
                        job: {
                          storeSalesMetadata: {
                            loyaltyFraction: 1,
                            transaction_upload_fraction: '1',
                          },
                          type: 'STORE_SALES_UPLOAD_FIRST_PARTY',
                        },
                      },
                      addConversionPayload: {
                        operations: [
                          {
                            create: {
                              consent: {
                                adPersonalization: 'UNSPECIFIED',
                                adUserData: 'UNSPECIFIED',
                              },
                              transaction_attribute: {
                                store_attribute: { store_code: 'store code' },
                                transaction_amount_micros: '100000000',
                                order_id: 'order id',
                                currency_code: 'INR',
                                transaction_date_time: '2019-10-14 16:45:18+05:30',
                              },
                              userIdentifiers: [
                                {
                                  hashedEmail:
                                    '6db61e6dcbcf2390e4a46af426f26a133a3bee45021422fc7ae86e9136f14110',
                                },
                              ],
                            },
                          },
                          {
                            create: {
                              consent: {
                                adPersonalization: 'UNSPECIFIED',
                                adUserData: 'UNSPECIFIED',
                              },
                              transaction_attribute: {
                                store_attribute: { store_code: 'store code2' },
                                transaction_amount_micros: '100000000',
                                order_id: 'order id',
                                currency_code: 'INR',
                                transaction_date_time: '2019-10-14 16:45:18+05:30',
                              },
                              userIdentifiers: [
                                {
                                  hashedEmail:
                                    '6db61e6dcbcf2390e4a46af426f26a133a3bee45021422fc7ae86e9136f14110',
                                },
                              ],
                            },
                          },
                        ],
                        enable_partial_failure: false,
                        enable_warnings: false,
                        validate_only: true,
                      },
                      executeJobPayload: { validate_only: true },
                    },
                    JSON_ARRAY: {},
                    XML: {},
                    FORM: {},
                  },
                  files: {},
                },
                {
                  version: '1',
                  type: 'REST',
                  method: 'POST',
                  endpoint: `https://googleads.googleapis.com/${API_VERSION}/customers/7693729833:uploadCallConversions`,
                  headers: {
                    Authorization: authHeader1,
                    'Content-Type': 'application/json',
                    'developer-token': secret3,
                  },
                  params: {
                    event: 'Order Completed',
                    customerId: '7693729833',
                    customVariables: [{ from: '', to: '' }],
                    properties: {
                      loyaltyFraction: 1,
                      order_id: 'order id',
                      currency: 'INR',
                      revenue: '100',
                      store_code: 'store code2',
                      email: '<EMAIL>',
                      gclid: 'gclid',
                      product_id: '123445',
                      quantity: 123,
                      callerId: '1234',
                      callStartDateTime: '2019-10-14T11:15:18.299Z',
                    },
                  },
                  body: {
                    JSON: {
                      conversions: [
                        {
                          consent: {
                            adPersonalization: 'UNSPECIFIED',
                            adUserData: 'UNSPECIFIED',
                          },
                          callerId: '1234',
                          callStartDateTime: '2019-10-14T11:15:18.299Z',
                          conversionDateTime: '2019-10-14 16:45:18+05:30',
                          conversionValue: 100,
                          currencyCode: 'INR',
                        },
                      ],
                      partialFailure: true,
                    },
                    JSON_ARRAY: {},
                    XML: {},
                    FORM: {},
                  },
                  files: {},
                },
              ],
              metadata: [
                {
                  secret: {
                    access_token: secret1,
                    refresh_token: 'efgh5678',
                    developer_token: secret3,
                  },
                  jobId: 3,
                  userId: 'u1',
                },
                {
                  secret: {
                    access_token: secret1,
                    refresh_token: 'efgh5678',
                    developer_token: secret3,
                  },
                  jobId: 4,
                  userId: 'u1',
                },
              ],
              batched: true,
              statusCode: 200,
              destination: {
                Config: {
                  rudderAccountId: '2Hsy2iFyoG5VLDd9wQcggHLMYFA',
                  customerId: '769-372-9833',
                  subAccount: false,
                  UserIdentifierSource: 'FIRST_PARTY',
                  conversionEnvironment: 'none',
                  defaultUserIdentifier: 'email',
                  hashUserIdentifier: true,
                  validateOnly: true,
                  eventsToOfflineConversionsTypeMapping: [
                    { from: 'Data Reading Guide', to: 'click' },
                    { from: 'Order Completed', to: 'store' },
                    { from: 'Sign-up - click', to: 'click' },
                    { from: 'Outbound click (rudderstack.com)', to: 'click' },
                    { from: 'Page view', to: 'click' },
                    { from: 'download', to: 'click' },
                    { from: 'Product Clicked', to: 'store' },
                    { from: 'Order Completed', to: 'call' },
                  ],
                  loginCustomerId: '**********',
                  eventsToConversionsNamesMapping: [
                    { from: 'Data Reading Guide', to: 'Data Reading Guide' },
                    { from: 'Order Completed', to: 'Order Completed' },
                    { from: 'Sign-up - click', to: 'Sign-up - click' },
                    {
                      from: 'Outbound click (rudderstack.com)',
                      to: 'Outbound click (rudderstack.com)',
                    },
                    { from: 'Page view', to: 'Page view' },
                    { from: 'Sign up completed', to: 'Sign-up - click' },
                    { from: 'download', to: 'Page view' },
                    { from: 'Product Clicked', to: 'Store sales' },
                  ],
                  authStatus: 'active',
                  customVariables: [{ from: '', to: '' }],
                },
              },
            },
            {
              batchedRequest: {
                version: '1',
                type: 'REST',
                method: 'POST',
                endpoint: `https://googleads.googleapis.com/${API_VERSION}/customers/**********:uploadClickConversions`,
                headers: {
                  Authorization: authHeader1,
                  'Content-Type': 'application/json',
                  'developer-token': secret3,
                },
                params: {
                  event: 'Sign-up - click',
                  customerId: '**********',
                  customVariables: [
                    { from: 'value', to: 'revenue' },
                    { from: 'total', to: 'cost' },
                  ],
                  properties: {
                    gbraid: 'gbraid',
                    wbraid: 'wbraid',
                    externalAttributionCredit: 10,
                    externalAttributionModel: 'externalAttributionModel',
                    conversionCustomVariable: 'conversionCustomVariable',
                    value: 'value',
                    merchantId: '9876merchantId',
                    feedCountryCode: 'feedCountryCode',
                    feedLanguageCode: 'feedLanguageCode',
                    localTransactionCost: 20,
                    products: [
                      {
                        product_id: '507f1f77bcf86cd799439011',
                        quantity: '2',
                        price: '50',
                        sku: '45790-32',
                        name: 'Monopoly: 3rd Edition',
                        position: '1',
                        category: 'cars',
                        url: 'https://www.example.com/product/path',
                        image_url: 'https://www.example.com/product/path.jpg',
                      },
                    ],
                    userIdentifierSource: 'FIRST_PARTY',
                    conversionEnvironment: 'WEB',
                    gclid: 'gclid',
                    conversionDateTime: '2022-01-01 12:32:45-08:00',
                    conversionValue: '1',
                    currency: 'GBP',
                  },
                },
                body: {
                  JSON: {
                    conversions: [
                      {
                        consent: {
                          adPersonalization: 'UNSPECIFIED',
                          adUserData: 'UNSPECIFIED',
                        },
                        externalAttributionData: {
                          externalAttributionCredit: 10,
                          externalAttributionModel: 'externalAttributionModel',
                        },
                        cartData: {
                          merchantId: 9876,
                          feedCountryCode: 'feedCountryCode',
                          feedLanguageCode: 'feedLanguageCode',
                          localTransactionCost: 20,
                          items: [
                            { productId: '507f1f77bcf86cd799439011', quantity: 2, unitPrice: 50 },
                          ],
                        },
                        userIdentifiers: [
                          {
                            userIdentifierSource: 'FIRST_PARTY',
                            hashedEmail:
                              '6db61e6dcbcf2390e4a46af426f26a133a3bee45021422fc7ae86e9136f14110',
                          },
                        ],
                        conversionEnvironment: 'WEB',
                        gclid: 'gclid',
                        conversionDateTime: '2022-01-01 12:32:45-08:00',
                        conversionValue: 1,
                        currencyCode: 'GBP',
                      },
                    ],
                    partialFailure: true,
                  },
                  JSON_ARRAY: {},
                  XML: {},
                  FORM: {},
                },
                files: {},
              },
              metadata: [
                {
                  secret: {
                    access_token: secret1,
                    refresh_token: 'efgh5678',
                    developer_token: secret3,
                  },
                  jobId: 1,
                  userId: 'u1',
                },
              ],
              batched: false,
              statusCode: 200,
              destination: {
                Config: {
                  customerId: '962-581-2972',
                  eventsToOfflineConversionsTypeMapping: [
                    { from: 'Sign up completed', to: 'click' },
                    { from: 'Download', to: 'call' },
                    { from: 'Promotion Clicked', to: 'click' },
                    { from: 'Product Searched', to: 'call' },
                  ],
                  eventsToConversionsNamesMapping: [
                    { from: 'Sign up completed', to: 'Sign-up - click' },
                    { from: 'Download', to: 'Page view' },
                    { from: 'Promotion Clicked', to: 'Sign-up - click' },
                    { from: 'Product Searched', to: 'search' },
                  ],
                  customVariables: [
                    { from: 'value', to: 'revenue' },
                    { from: 'total', to: 'cost' },
                  ],
                  UserIdentifierSource: 'THIRD_PARTY',
                  conversionEnvironment: 'WEB',
                  hashUserIdentifier: true,
                  defaultUserIdentifier: 'email',
                  validateOnly: false,
                  rudderAccountId: '25u5whFH7gVTnCiAjn4ykoCLGoC',
                },
              },
            },
            {
              batchedRequest: {
                version: '1',
                type: 'REST',
                method: 'POST',
                endpoint: `https://googleads.googleapis.com/${API_VERSION}/customers/**********:uploadCallConversions`,
                headers: {
                  Authorization: authHeader1,
                  'Content-Type': 'application/json',
                  'developer-token': secret3,
                },
                params: {
                  event: 'search',
                  customerId: '**********',
                  customVariables: [
                    { from: 'value', to: 'revenue' },
                    { from: 'total', to: 'cost' },
                  ],
                  properties: {
                    externalAttributionCredit: 10,
                    externalAttributionModel: 'externalAttributionModel',
                    merchantId: 'merchantId',
                    feedCountryCode: 'feedCountryCode',
                    feedLanguageCode: 'feedLanguageCode',
                    localTransactionCost: 20,
                    products: [
                      {
                        product_id: '507f1f77bcf86cd799439011',
                        quantity: '2',
                        price: '50',
                        sku: '45790-32',
                        name: 'Monopoly: 3rd Edition',
                        position: '1',
                        category: 'cars',
                        url: 'https://www.example.com/product/path',
                        image_url: 'https://www.example.com/product/path.jpg',
                      },
                    ],
                    userIdentifierSource: 'FIRST_PARTY',
                    conversionEnvironment: 'WEB',
                    gclid: 'gclid',
                    conversionCustomVariable: 'conversionCustomVariable',
                    value: 'value',
                    callerId: 'callerId',
                    callStartDateTime: '2022-08-28 15:01:30+05:30',
                    conversionDateTime: '2022-01-01 12:32:45-08:00',
                    conversionValue: '1',
                    currency: 'GBP',
                  },
                },
                body: {
                  JSON: {
                    conversions: [
                      {
                        consent: {
                          adPersonalization: 'UNSPECIFIED',
                          adUserData: 'UNSPECIFIED',
                        },
                        callerId: 'callerId',
                        callStartDateTime: '2022-08-28 15:01:30+05:30',
                        conversionDateTime: '2022-01-01 12:32:45-08:00',
                        conversionValue: 1,
                        currencyCode: 'GBP',
                      },
                    ],
                    partialFailure: true,
                  },
                  JSON_ARRAY: {},
                  XML: {},
                  FORM: {},
                },
                files: {},
              },
              metadata: [
                {
                  secret: {
                    access_token: secret1,
                    refresh_token: 'efgh5678',
                    developer_token: secret3,
                  },
                  jobId: 2,
                  userId: 'u1',
                },
              ],
              batched: false,
              statusCode: 200,
              destination: {
                Config: {
                  customerId: '962-581-2972',
                  eventsToOfflineConversionsTypeMapping: [
                    { from: 'Sign up completed', to: 'click' },
                    { from: 'Download', to: 'call' },
                    { from: 'Promotion Clicked', to: 'click' },
                    { from: 'Product Searched', to: 'call' },
                  ],
                  eventsToConversionsNamesMapping: [
                    { from: 'Sign up completed', to: 'Sign-up - click' },
                    { from: 'Download', to: 'Page view' },
                    { from: 'Promotion Clicked', to: 'Sign-up - click' },
                    { from: 'Product Searched', to: 'search' },
                  ],
                  customVariables: [
                    { from: 'value', to: 'revenue' },
                    { from: 'total', to: 'cost' },
                  ],
                  UserIdentifierSource: 'THIRD_PARTY',
                  conversionEnvironment: 'WEB',
                  hashUserIdentifier: true,
                  defaultUserIdentifier: 'phone',
                  validateOnly: false,
                  rudderAccountId: '25u5whFH7gVTnCiAjn4ykoCLGoC',
                },
              },
            },
            {
              metadata: [
                {
                  secret: {
                    access_token: secret1,
                    refresh_token: 'efgh5678',
                    developer_token: secret3,
                  },
                  jobId: 5,
                  userId: 'u1',
                },
              ],
              batched: false,
              statusCode: 400,
              error:
                "Event name 'order completed' is not present in the mapping provided in the dashboard.",
              statTags: {
                destType: 'GOOGLE_ADWORDS_OFFLINE_CONVERSIONS',
                errorCategory: 'dataValidation',
                errorType: 'configuration',
                feature: 'router',
                implementation: 'native',
                module: 'destination',
              },
              destination: {
                Config: {
                  rudderAccountId: '2Hsy2iFyoG5VLDd9wQcggHLMYFA',
                  customerId: '769-372-9833',
                  subAccount: false,
                  UserIdentifierSource: 'FIRST_PARTY',
                  conversionEnvironment: 'none',
                  defaultUserIdentifier: 'email',
                  hashUserIdentifier: true,
                  validateOnly: true,
                  eventsToOfflineConversionsTypeMapping: [
                    { from: 'Data Reading Guide', to: 'click' },
                    { from: 'Sign-up - click', to: 'click' },
                    { from: 'Outbound click (rudderstack.com)', to: 'click' },
                    { from: 'Page view', to: 'click' },
                    { from: 'download', to: 'click' },
                    { from: 'Product Clicked', to: 'store' },
                    { from: 'Order Completed', to: 'call' },
                  ],
                  loginCustomerId: '**********',
                  eventsToConversionsNamesMapping: [
                    { from: 'Data Reading Guide', to: 'Data Reading Guide' },
                    { from: 'Sign-up - click', to: 'Sign-up - click' },
                    {
                      from: 'Outbound click (rudderstack.com)',
                      to: 'Outbound click (rudderstack.com)',
                    },
                    { from: 'Page view', to: 'Page view' },
                    { from: 'Sign up completed', to: 'Sign-up - click' },
                    { from: 'download', to: 'Page view' },
                    { from: 'Product Clicked', to: 'Store sales' },
                  ],
                  authStatus: 'active',
                  customVariables: [{ from: '', to: '' }],
                },
              },
            },
          ],
        },
      },
    },
    mockFns: timestampMock,
  },
  {
    name: 'google_adwords_offline_conversions',
    description: 'Test 1 should include destination when single store sales event is sent',
    feature: 'router',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: {
          input: [
            {
              message: {
                type: 'track',
                event: 'Order Completed',
                sentAt: '2024-05-09T00:02:48.319Z',
                userId: '7fe0de4847f6dafb0cba694ef725404a',
                channel: 'sources',
                context: {
                  banner: {
                    key: 'CS',
                    domain: 'www.champssports.com',
                  },
                  traits: {
                    email: '<EMAIL>',
                    address: {
                      city: 'homestead',
                      state: 'fl',
                      country: 'usa',
                      postalCode: '33032',
                    },
                    lastName: 'wick',
                    firstName: 'john',
                  },
                  privacy: {
                    ccpa: true,
                  },
                  sources: {
                    job_id: '123344545565466',
                    version: 'v1.48.7',
                    job_run_id: 'cou1407gdjb6rkrrtv5g',
                    task_run_id: 'cou1407gdjb6rkrrtv6g',
                  },
                  snowflake: {
                    ID: '44acd2006efb6b7d1a0eaf0da2b05b69',
                    TAX: 8.05,
                    NAME: 'johnwick',
                    PHONE: '',
                    TOTAL: 115,
                    email: '<EMAIL>',
                    BANNER: 'CS',
                    COUPON: '[null]',
                    REVENUE: 123.05,
                    CATEGORY: 'Retail',
                    CURRENCY: 'USD',
                    DISCOUNT: 0,
                    LASTNAME: 'wick',
                    ORDER_ID: '12343-4886-294995',
                    PRODUCTS:
                      '[{"sku":"C2302100","product_id":816827,"category":"1","size":"10.5","name":"NIKE AF1 \'07 AN21-WH/BK","brand":"NIKE INC","variant":"WHITE/BLACK","class":"MENS","price":"115.0","division":"CASUAL-ATHLETIC","quantity":"1","discountFlag":"false"}]',
                    RUDDERID: 'UNAVAILABLE',
                    SHIPPING: 'n/a',
                    STORE_ID: '14540',
                    SUBTOTAL: 115,
                    FIRSTNAME: 'john',
                    MESSAGEID: 'UNAVAILABLE',
                    TIMESTAMP: '2024-05-07T17:27:28.262Z',
                    TOTAL_VAT: 123.05,
                    EVENT_DATE: '2024-05-07T00:00:00Z',
                    STORE_NAME: 'CHAMPS                        ',
                    DISCOUNT_VAT: 0,
                    IS_E_RECEIPT: '1',
                    SUBTOTAL_VAT: 123.05,
                    USER_ADDRESS: '',
                    FLX_CARDNUMBER: '**************',
                    PAYMENT_METHOD: null,
                    ACCOUNT_ADDRESS: null,
                    CM_PHONE_NUMBER: '**********',
                    SHIPPING_METHOD: 'n/a',
                    STORE_ADDR_CITY: 'CUTLER BAY               ',
                    CM_BILL_ADDRESS1: '13020 SW 256TH ST',
                    STORE_ADDR_STATE: 'FL',
                    STORE_ADDR_STREET:
                      'SOUTHLAND MALL           20505 SOUTH DIXIE HWY    SPACE 1401               ',
                    STORE_ADDR_COUNTRY: 'UNITED STATES',
                    STORE_ADDR_ZIPCODE: '33189        ',
                    ACCOUNT_ADDRESS_CITY: 'HOMESTEAD',
                    BILLING_ADDRESS_CITY: 'HOMESTEAD',
                    SHIP_TO_ADDRESS_CITY: 'UNAVAILABLE',
                    ACCOUNT_ADDRESS_STATE: 'FL',
                    BILLING_ADDRESS_STATE: 'FL',
                    SHIP_TO_ADDRESS_STATE: 'UNAVAILABLE',
                    SHIP_TO_ADDRESS_STREET: 'UNAVAILABLE',
                    ACCOUNT_ADDRESS_COUNTRY: 'US',
                    BILLING_ADDRESS_COUNTRY: 'USA',
                    SHIP_TO_ADDRESS_COUNTRY: 'UNAVAILABLE',
                    SHIP_TO_ADDRESS_POSTALCODE: 'UNAVAILABLE',
                    ACCOUNT_ADDRESS_POSTAL_CODE: '33032',
                    BILLING_ADDRESS_POSTAL_CODE: '33032',
                  },
                  account_id: 'xxxxxxxxxx',
                  account_mcc: '**********',
                },
                recordId: '1230',
                rudderId: '35d5060a-2756-45d1-9808-cae9aec19166',
                messageId: '23d5060b-2756-45c1-9108-c229aec19126',
                timestamp: '2024-05-07 17:27:28-00:00',
                properties: {
                  value: 123.05,
                  currency: 'USD',
                  order_id: '12343-4886-294995',
                  products: [
                    {
                      sku: 'C2302100',
                      price: 115,
                      quantity: '1',
                    },
                  ],
                  conversionDateTime: '2024-05-07 17:27:28-00:00',
                },
                receivedAt: '2024-05-09T00:02:43.864Z',
                request_ip: '************',
                originalTimestamp: '2024-05-09T00:02:48.319Z',
              },
              metadata: {
                secret: {
                  access_token: secret1,
                  refresh_token: 'efgh5678',
                  developer_token: secret3,
                },
                jobId: 1,
                userId: 'u1',
              },
              destination: {
                Config: {
                  // customerId: '962-581-2972',
                  customerId: '{{ event.context.account_mcc || "**********" }}',
                  subAccount: false,
                  loginCustomerId: '{{ event.context.account_mcc || "**********" }}',
                  eventsToOfflineConversionsTypeMapping: [
                    {
                      from: 'Order Completed',
                      to: 'store',
                    },
                  ],
                  eventsToConversionsNamesMapping: [
                    {
                      from: 'Order Completed',
                      to: 'Store sales',
                    },
                  ],
                  UserIdentifierSource: 'FIRST_PARTY',
                  conversionEnvironment: 'none',
                  defaultUserIdentifier: 'email',
                  hashUserIdentifier: true,
                  validateOnly: false,
                  eventDelivery: false,
                  eventDeliveryTS: *************,
                  rudderAccountId: '25u5whFH7gVTnCiAjn4ykoCLGoC',
                },
              },
            },
          ],
          destType: 'google_adwords_offline_conversions',
        },
        method: 'POST',
      },
    },
    output: {
      response: {
        status: 200,
        body: {
          output: [
            {
              batchedRequest: {
                version: '1',
                type: 'REST',
                method: 'POST',
                endpoint: `https://googleads.googleapis.com/${API_VERSION}/customers/**********/offlineUserDataJobs`,
                headers: {
                  Authorization: authHeader1,
                  'Content-Type': 'application/json',
                  'developer-token': secret3,
                },
                params: { event: 'Store sales', customerId: '**********' },
                body: {
                  JSON: {
                    event: '**********',
                    isStoreConversion: true,
                    createJobPayload: {
                      job: {
                        type: 'STORE_SALES_UPLOAD_FIRST_PARTY',
                        storeSalesMetadata: {
                          loyaltyFraction: '1',
                          transaction_upload_fraction: '1',
                        },
                      },
                    },
                    addConversionPayload: {
                      operations: [
                        {
                          create: {
                            transaction_attribute: {
                              transaction_amount_micros: '123050000',
                              order_id: '12343-4886-294995',
                              currency_code: 'USD',
                              transaction_date_time: '2019-10-14 16:45:18+05:30',
                            },
                            userIdentifiers: [
                              {
                                hashedEmail:
                                  'cd54e8f2e90e2a092a153f7e27e7b47a8ad29adb7943a05d749f0f9836935a2f',
                              },
                            ],
                            consent: {
                              adPersonalization: 'UNSPECIFIED',
                              adUserData: 'UNSPECIFIED',
                            },
                          },
                        },
                      ],
                      enable_partial_failure: false,
                      enable_warnings: false,
                      validate_only: false,
                    },
                    executeJobPayload: { validate_only: false },
                  },
                  JSON_ARRAY: {},
                  XML: {},
                  FORM: {},
                },
                files: {},
              },
              metadata: [
                {
                  secret: {
                    access_token: secret1,
                    refresh_token: 'efgh5678',
                    developer_token: secret3,
                  },
                  jobId: 1,
                  userId: 'u1',
                },
              ],
              destination: {
                Config: {
                  // customerId: '962-581-2972',
                  customerId: '**********',
                  subAccount: false,
                  loginCustomerId: '**********',
                  eventsToOfflineConversionsTypeMapping: [
                    {
                      from: 'Order Completed',
                      to: 'store',
                    },
                  ],
                  eventsToConversionsNamesMapping: [
                    {
                      from: 'Order Completed',
                      to: 'Store sales',
                    },
                  ],
                  UserIdentifierSource: 'FIRST_PARTY',
                  conversionEnvironment: 'none',
                  defaultUserIdentifier: 'email',
                  hashUserIdentifier: true,
                  validateOnly: false,
                  eventDelivery: false,
                  eventDeliveryTS: *************,
                  rudderAccountId: '25u5whFH7gVTnCiAjn4ykoCLGoC',
                },
              },
              batched: true,
              statusCode: 200,
            },
          ],
        },
      },
    },
    mockFns: timestampMock,
  },
];
