import { ProcessorTestData } from '../../../testTypes';
import { Metadata } from '../../../../../src/types';

const baseMetadata: Partial<Metadata> = {
  sourceId: 'default-sourceId',
  workspaceId: 'default-workspaceId',
  namespace: 'default-namespace',
  instanceId: 'default-instance',
  sourceType: 'default-source-type',
  sourceCategory: 'default-category',
  trackingPlanId: 'default-tracking-plan',
  trackingPlanVersion: 1,
  sourceTpConfig: {},
  mergedTpConfig: {},
  destinationId: 'default-destinationId',
  jobRunId: 'default-job-run',
  jobId: 1,
  sourceBatchId: 'default-batch',
  sourceJobId: 'default-source-job',
  sourceJobRunId: 'default-source-job-run',
  sourceTaskId: 'default-task',
  sourceTaskRunId: 'default-task-run',
  recordId: {},
  destinationType: 'default-destination-type',
  messageId: 'default-message-id',
  oauthAccessToken: 'default-token',
  messageIds: ['default-message-id'],
  rudderId: 'default-rudder-id',
  receivedAt: '2025-01-06T04:00:49.698Z',
  eventName: 'default-event',
  eventType: 'default-type',
  sourceDefinitionId: 'default-source-def',
  destinationDefinitionId: 'default-dest-def',
  transformationId: 'default-transform',
  dontBatch: false,
};

export const trackTestData: ProcessorTestData[] = [
  {
    id: 'iterable-track-test-1',
    name: 'iterable',
    description: 'Track call to add event with user',
    scenario: 'Business',
    successCriteria:
      'Response should contain status code 200 and it should contain event properties and event name',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        method: 'POST',
        body: [
          {
            message: {
              anonymousId: 'anonId',
              event: 'Email Opened',
              type: 'track',
              context: {},
              properties: {
                subject: 'resume validate',
                sendtime: '2020-01-01',
                sendlocation: '<EMAIL>',
              },
              sentAt: '2020-08-28T16:26:16.473Z',
              originalTimestamp: '2020-08-28T16:26:06.468Z',
            },
            metadata: baseMetadata,
            destination: {
              ID: '123',
              Name: 'iterable',
              DestinationDefinition: {
                ID: '123',
                Name: 'iterable',
                DisplayName: 'Iterable',
                Config: {},
              },
              Config: {
                apiKey: 'testApiKey',
                dataCenter: 'USDC',
                preferUserId: false,
                trackAllPages: true,
                trackNamedPages: false,
                mapToSingleEvent: false,
                trackCategorisedPages: false,
              },
              Enabled: true,
              WorkspaceID: '123',
              Transformations: [],
              RevisionID: 'default-revision',
              IsProcessorEnabled: true,
              IsConnectionEnabled: true,
            },
          },
        ],
      },
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              userId: '',
              method: 'POST',
              endpoint: 'https://api.iterable.com/api/events/track',
              headers: {
                api_key: 'testApiKey',
                'Content-Type': 'application/json',
              },
              params: {},
              body: {
                JSON: {
                  userId: 'anonId',
                  createdAt: 1598631966468,
                  eventName: 'Email Opened',
                  dataFields: {
                    subject: 'resume validate',
                    sendtime: '2020-01-01',
                    sendlocation: '<EMAIL>',
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
            },
            metadata: baseMetadata,
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    id: 'iterable-track-test-2',
    name: 'iterable',
    description: 'Track call for product added event with all properties',
    scenario: 'Business',
    successCriteria:
      'Response should contain status code 200 and it should contain event name and all properties',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        method: 'POST',
        body: [
          {
            message: {
              type: 'track',
              sentAt: '2020-08-28T16:26:16.473Z',
              userId: 'userId',
              channel: 'web',
              context: {
                os: {
                  name: '',
                  version: '1.12.3',
                },
                app: {
                  name: 'RudderLabs JavaScript SDK',
                  build: '1.0.0',
                  version: '1.1.11',
                  namespace: 'com.rudderlabs.javascript',
                },
                traits: {
                  email: '<EMAIL>',
                },
                locale: 'en-US',
                device: {
                  token: 'token',
                  id: 'id',
                  type: 'ios',
                },
                screen: {
                  density: 2,
                },
                library: {
                  name: 'RudderLabs JavaScript SDK',
                  version: '1.1.11',
                },
                campaign: {},
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.16; rv:84.0) Gecko/20100101 Firefox/84.0',
              },
              rudderId: '227egb53wnacyz7f5hopt3jwriuwwk8n2y8i',
              messageId: '40q64xrajd4kqt5174iy8889da8kjij55u85',
              anonymousId: 'anonId',
              originalTimestamp: '2020-08-28T16:26:06.468Z',
              event: 'product added',
              properties: {
                campaignId: '1',
                templateId: '0',
                orderId: 10000,
                total: 1000,
                products: [
                  {
                    product_id: '507f1f77bcf86cd799439011',
                    sku: '45790-32',
                    name: 'Monopoly: 3rd Edition',
                    price: '19',
                    position: '1',
                    category: 'cars',
                    url: 'https://www.example.com/product/path',
                    image_url: 'https://www.example.com/product/path.jpg',
                    quantity: '2',
                  },
                  {
                    product_id: '507f1f77bcf86cd7994390112',
                    sku: '45790-322',
                    name: 'Monopoly: 3rd Edition2',
                    price: '192',
                    quantity: 22,
                    position: '12',
                    category: 'Cars2',
                    url: 'https://www.example.com/product/path2',
                    image_url: 'https://www.example.com/product/path.jpg2',
                  },
                ],
              },
            },
            metadata: baseMetadata,
            destination: {
              ID: '123',
              Name: 'iterable',
              DestinationDefinition: {
                ID: '123',
                Name: 'iterable',
                DisplayName: 'Iterable',
                Config: {},
              },
              Config: {
                apiKey: 'testApiKey',
                dataCenter: 'USDC',
                preferUserId: false,
                trackAllPages: true,
                trackNamedPages: false,
                mapToSingleEvent: false,
                trackCategorisedPages: false,
              },
              Enabled: true,
              WorkspaceID: '123',
              Transformations: [],
              RevisionID: 'default-revision',
              IsProcessorEnabled: true,
              IsConnectionEnabled: true,
            },
          },
        ],
      },
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              userId: '',
              method: 'POST',
              endpoint: 'https://api.iterable.com/api/commerce/updateCart',
              headers: {
                api_key: 'testApiKey',
                'Content-Type': 'application/json',
              },
              params: {},
              body: {
                JSON: {
                  user: {
                    email: '<EMAIL>',
                    dataFields: {
                      email: '<EMAIL>',
                    },
                    userId: 'userId',
                    preferUserId: false,
                    mergeNestedObjects: true,
                  },
                  items: [
                    {
                      id: '507f1f77bcf86cd799439011',
                      sku: '45790-32',
                      name: 'Monopoly: 3rd Edition',
                      categories: ['cars'],
                      price: 19,
                      quantity: 2,
                      imageUrl: 'https://www.example.com/product/path.jpg',
                      url: 'https://www.example.com/product/path',
                    },
                    {
                      id: '507f1f77bcf86cd7994390112',
                      sku: '45790-322',
                      name: 'Monopoly: 3rd Edition2',
                      categories: ['Cars2'],
                      price: 192,
                      quantity: 22,
                      imageUrl: 'https://www.example.com/product/path.jpg2',
                      url: 'https://www.example.com/product/path2',
                    },
                  ],
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
            },
            metadata: baseMetadata,
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    id: 'iterable-track-test-3',
    name: 'iterable',
    description: 'Track call for order completed event with all properties',
    scenario: 'Business',
    successCriteria:
      'Response should contain status code 200 and it should contain event name and all properties',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        method: 'POST',
        body: [
          {
            message: {
              type: 'track',
              sentAt: '2020-08-28T16:26:16.473Z',
              userId: 'userId',
              channel: 'web',
              context: {
                os: {
                  name: '',
                  version: '1.12.3',
                },
                app: {
                  name: 'RudderLabs JavaScript SDK',
                  build: '1.0.0',
                  version: '1.1.11',
                  namespace: 'com.rudderlabs.javascript',
                },
                traits: {
                  email: '<EMAIL>',
                },
                locale: 'en-US',
                device: {
                  token: 'token',
                  id: 'id',
                  type: 'ios',
                },
                screen: {
                  density: 2,
                },
                library: {
                  name: 'RudderLabs JavaScript SDK',
                  version: '1.1.11',
                },
                campaign: {},
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.16; rv:84.0) Gecko/20100101 Firefox/84.0',
              },
              rudderId: 'npe99e7yc7apntgd9roobt2n8i7262rsg6vr',
              messageId: '68ygodw5mj88lmjm5sm765tatvscrucqo6kx',
              anonymousId: 'anonId',
              originalTimestamp: '2020-08-28T16:26:06.468Z',
              event: 'order completed',
              properties: {
                orderId: 10000,
                total: '1000',
                campaignId: '123456',
                templateId: '1213458',
                products: [
                  {
                    product_id: '507f1f77bcf86cd799439011',
                    sku: '45790-32',
                    name: 'Monopoly: 3rd Edition',
                    price: '19',
                    position: '1',
                    category: 'cars',
                    url: 'https://www.example.com/product/path',
                    image_url: 'https://www.example.com/product/path.jpg',
                    quantity: '2',
                  },
                  {
                    product_id: '507f1f77bcf86cd7994390112',
                    sku: '45790-322',
                    name: 'Monopoly: 3rd Edition2',
                    price: '192',
                    quantity: 22,
                    position: '12',
                    category: 'Cars2',
                    url: 'https://www.example.com/product/path2',
                    image_url: 'https://www.example.com/product/path.jpg2',
                  },
                ],
              },
            },
            metadata: baseMetadata,
            destination: {
              ID: '123',
              Name: 'iterable',
              DestinationDefinition: {
                ID: '123',
                Name: 'iterable',
                DisplayName: 'Iterable',
                Config: {},
              },
              Config: {
                apiKey: 'testApiKey',
                dataCenter: 'USDC',
                preferUserId: false,
                trackAllPages: true,
                trackNamedPages: false,
                mapToSingleEvent: false,
                trackCategorisedPages: false,
              },
              Enabled: true,
              WorkspaceID: '123',
              Transformations: [],
              RevisionID: 'default-revision',
              IsProcessorEnabled: true,
              IsConnectionEnabled: true,
            },
          },
        ],
      },
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              userId: '',
              method: 'POST',
              endpoint: 'https://api.iterable.com/api/commerce/trackPurchase',
              headers: {
                api_key: 'testApiKey',
                'Content-Type': 'application/json',
              },
              params: {},
              body: {
                JSON: {
                  dataFields: {
                    orderId: 10000,
                    total: '1000',
                    campaignId: '123456',
                    templateId: '1213458',
                    products: [
                      {
                        product_id: '507f1f77bcf86cd799439011',
                        sku: '45790-32',
                        name: 'Monopoly: 3rd Edition',
                        price: '19',
                        position: '1',
                        category: 'cars',
                        url: 'https://www.example.com/product/path',
                        image_url: 'https://www.example.com/product/path.jpg',
                        quantity: '2',
                      },
                      {
                        product_id: '507f1f77bcf86cd7994390112',
                        sku: '45790-322',
                        name: 'Monopoly: 3rd Edition2',
                        price: '192',
                        quantity: 22,
                        position: '12',
                        category: 'Cars2',
                        url: 'https://www.example.com/product/path2',
                        image_url: 'https://www.example.com/product/path.jpg2',
                      },
                    ],
                  },
                  id: '10000',
                  createdAt: 1598631966468,
                  campaignId: 123456,
                  templateId: 1213458,
                  total: 1000,
                  user: {
                    email: '<EMAIL>',
                    dataFields: {
                      email: '<EMAIL>',
                    },
                    userId: 'userId',
                    preferUserId: false,
                    mergeNestedObjects: true,
                  },
                  items: [
                    {
                      id: '507f1f77bcf86cd799439011',
                      sku: '45790-32',
                      name: 'Monopoly: 3rd Edition',
                      categories: ['cars'],
                      price: 19,
                      quantity: 2,
                      imageUrl: 'https://www.example.com/product/path.jpg',
                      url: 'https://www.example.com/product/path',
                    },
                    {
                      id: '507f1f77bcf86cd7994390112',
                      sku: '45790-322',
                      name: 'Monopoly: 3rd Edition2',
                      categories: ['Cars2'],
                      price: 192,
                      quantity: 22,
                      imageUrl: 'https://www.example.com/product/path.jpg2',
                      url: 'https://www.example.com/product/path2',
                    },
                  ],
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
            },
            metadata: baseMetadata,
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    id: 'iterable-track-test-4',
    name: 'iterable',
    description: 'Track call for custom event with all properties',
    scenario: 'Business',
    successCriteria:
      'Response should contain status code 200 and it should contain custom event name and all properties',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        method: 'POST',
        body: [
          {
            message: {
              type: 'track',
              sentAt: '2020-08-28T16:26:16.473Z',
              userId: 'userId',
              channel: 'web',
              context: {
                os: {
                  name: '',
                  version: '1.12.3',
                },
                app: {
                  name: 'RudderLabs JavaScript SDK',
                  build: '1.0.0',
                  version: '1.1.11',
                  namespace: 'com.rudderlabs.javascript',
                },
                traits: {
                  email: '<EMAIL>',
                },
                locale: 'en-US',
                device: {
                  token: 'token',
                  id: 'id',
                  type: 'ios',
                },
                screen: {
                  density: 2,
                },
                library: {
                  name: 'RudderLabs JavaScript SDK',
                  version: '1.1.11',
                },
                campaign: {},
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.16; rv:84.0) Gecko/20100101 Firefox/84.0',
              },
              rudderId: 'id2nbnto38rw1v5wiqyc81xe61c7t420zpjb',
              messageId: '28pdlaljhp7i1woa7b0fhj47rlz3g4z2pvdw',
              anonymousId: 'anonId',
              originalTimestamp: '2020-08-28T16:26:06.468Z',
              event: 'test track event GA3',
              properties: {
                campaignId: '1',
                templateId: '0',
                user_actual_id: 12345,
                category: 'test-category',
                email: '<EMAIL>',
                user_actual_role: 'system_admin, system_user',
              },
            },
            metadata: baseMetadata,
            destination: {
              ID: '123',
              Name: 'iterable',
              DestinationDefinition: {
                ID: '123',
                Name: 'iterable',
                DisplayName: 'Iterable',
                Config: {},
              },
              Config: {
                apiKey: 'testApiKey',
                dataCenter: 'USDC',
                preferUserId: false,
                trackAllPages: true,
                trackNamedPages: false,
                mapToSingleEvent: false,
                trackCategorisedPages: false,
              },
              Enabled: true,
              WorkspaceID: '123',
              Transformations: [],
              RevisionID: 'default-revision',
              IsProcessorEnabled: true,
              IsConnectionEnabled: true,
            },
          },
        ],
      },
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              userId: '',
              method: 'POST',
              endpoint: 'https://api.iterable.com/api/events/track',
              headers: {
                api_key: 'testApiKey',
                'Content-Type': 'application/json',
              },
              params: {},
              body: {
                JSON: {
                  email: '<EMAIL>',
                  dataFields: {
                    campaignId: '1',
                    templateId: '0',
                    user_actual_id: 12345,
                    category: 'test-category',
                    email: '<EMAIL>',
                    user_actual_role: 'system_admin, system_user',
                  },
                  userId: 'userId',
                  eventName: 'test track event GA3',
                  createdAt: 1598631966468,
                  campaignId: 1,
                  templateId: 0,
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
            },
            metadata: baseMetadata,
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    id: 'iterable-track-test-5',
    name: 'iterable',
    description: 'Track call for product added event with product info as properties',
    scenario: 'Business',
    successCriteria:
      'Response should contain status code 200 and it should contain event name and all properties',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        method: 'POST',
        body: [
          {
            message: {
              type: 'track',
              sentAt: '2020-08-28T16:26:16.473Z',
              userId: 'userId',
              channel: 'web',
              context: {
                os: {
                  name: '',
                  version: '1.12.3',
                },
                app: {
                  name: 'RudderLabs JavaScript SDK',
                  build: '1.0.0',
                  version: '1.1.11',
                  namespace: 'com.rudderlabs.javascript',
                },
                traits: {
                  email: '<EMAIL>',
                },
                locale: 'en-US',
                device: {
                  token: 'token',
                  id: 'id',
                  type: 'ios',
                },
                screen: {
                  density: 2,
                },
                library: {
                  name: 'RudderLabs JavaScript SDK',
                  version: '1.1.11',
                },
                campaign: {},
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.16; rv:84.0) Gecko/20100101 Firefox/84.0',
              },
              rudderId: 'gm8wm4385x4kfzl464h7tjtob474i17anotc',
              messageId: 'jslc0490479rziix9h0ya6z6qpn2taqmryro',
              anonymousId: 'anonId',
              originalTimestamp: '2020-08-28T16:26:06.468Z',
              event: 'product added',
              properties: {
                price: 797,
                variant: 'Oak',
                quantity: 1,
                quickship: true,
                full_price: 1328,
                product_id: 10606,
                non_interaction: 1,
                sku: 'JB24691400-W05',
                name: 'Vira Console Cabinet',
                cart_id: 'bd9b8dbf4ef8ee01d4206b04fe2ee6ae',
              },
            },
            metadata: baseMetadata,
            destination: {
              ID: '123',
              Name: 'iterable',
              DestinationDefinition: {
                ID: '123',
                Name: 'iterable',
                DisplayName: 'Iterable',
                Config: {},
              },
              Config: {
                apiKey: 'testApiKey',
                dataCenter: 'USDC',
                preferUserId: false,
                trackAllPages: true,
                trackNamedPages: false,
                mapToSingleEvent: false,
                trackCategorisedPages: false,
              },
              Enabled: true,
              WorkspaceID: '123',
              Transformations: [],
              RevisionID: 'default-revision',
              IsProcessorEnabled: true,
              IsConnectionEnabled: true,
            },
          },
        ],
      },
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              userId: '',
              method: 'POST',
              endpoint: 'https://api.iterable.com/api/commerce/updateCart',
              headers: {
                api_key: 'testApiKey',
                'Content-Type': 'application/json',
              },
              params: {},
              body: {
                JSON: {
                  user: {
                    email: '<EMAIL>',
                    dataFields: {
                      email: '<EMAIL>',
                    },
                    userId: 'userId',
                    preferUserId: false,
                    mergeNestedObjects: true,
                  },
                  items: [
                    {
                      id: 10606,
                      sku: 'JB24691400-W05',
                      name: 'Vira Console Cabinet',
                      price: 797,
                      quantity: 1,
                    },
                  ],
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
            },
            metadata: baseMetadata,
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    id: 'iterable-track-test-6',
    name: 'iterable',
    description: 'Track call for product added event with product info as properties',
    scenario: 'Business',
    successCriteria:
      'Response should contain status code 200 and it should contain event name and all properties',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        method: 'POST',
        body: [
          {
            message: {
              type: 'track',
              sentAt: '2020-08-28T16:26:16.473Z',
              userId: 'userId',
              channel: 'web',
              context: {
                os: {
                  name: '',
                  version: '1.12.3',
                },
                app: {
                  name: 'RudderLabs JavaScript SDK',
                  build: '1.0.0',
                  version: '1.1.11',
                  namespace: 'com.rudderlabs.javascript',
                },
                traits: {
                  email: '<EMAIL>',
                },
                locale: 'en-US',
                device: {
                  token: 'token',
                  id: 'id',
                  type: 'ios',
                },
                screen: {
                  density: 2,
                },
                library: {
                  name: 'RudderLabs JavaScript SDK',
                  version: '1.1.11',
                },
                campaign: {},
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.16; rv:84.0) Gecko/20100101 Firefox/84.0',
              },
              rudderId: '4osg2zcrsrh2accmbijxm0zqixtxyrsun8uc',
              messageId: 'x901bx7paxtr7ktja5mhd1mi8q4lr5vlrl2x',
              anonymousId: 'anonId',
              originalTimestamp: '2020-08-28T16:26:06.468Z',
              event: 'product added',
              properties: {
                campaignId: '1',
                templateId: '0',
                orderId: 10000,
                total: 1000,
                product_id: '507f1f77bcf86cd7994390112',
                sku: '45790-322',
                name: 'Monopoly: 3rd Edition2',
                price: '192',
                quantity: 22,
                position: '12',
                category: 'Cars2',
                url: 'https://www.example.com/product/path2',
                image_url: 'https://www.example.com/product/path.jpg2',
              },
            },
            metadata: baseMetadata,
            destination: {
              ID: '123',
              Name: 'iterable',
              DestinationDefinition: {
                ID: '123',
                Name: 'iterable',
                DisplayName: 'Iterable',
                Config: {},
              },
              Config: {
                apiKey: 'testApiKey',
                dataCenter: 'USDC',
                preferUserId: false,
                trackAllPages: true,
                trackNamedPages: false,
                mapToSingleEvent: false,
                trackCategorisedPages: false,
              },
              Enabled: true,
              WorkspaceID: '123',
              Transformations: [],
              RevisionID: 'default-revision',
              IsProcessorEnabled: true,
              IsConnectionEnabled: true,
            },
          },
        ],
      },
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              userId: '',
              method: 'POST',
              endpoint: 'https://api.iterable.com/api/commerce/updateCart',
              headers: {
                api_key: 'testApiKey',
                'Content-Type': 'application/json',
              },
              params: {},
              body: {
                JSON: {
                  user: {
                    email: '<EMAIL>',
                    dataFields: {
                      email: '<EMAIL>',
                    },
                    userId: 'userId',
                    preferUserId: false,
                    mergeNestedObjects: true,
                  },
                  items: [
                    {
                      price: 192,
                      url: 'https://www.example.com/product/path2',
                      sku: '45790-322',
                      name: 'Monopoly: 3rd Edition2',
                      id: '507f1f77bcf86cd7994390112',
                      quantity: 22,
                      imageUrl: 'https://www.example.com/product/path.jpg2',
                      categories: ['Cars2'],
                    },
                  ],
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
            },
            metadata: baseMetadata,
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    id: 'iterable-track-test-7',
    name: 'iterable',
    description: 'Track call for order completed event with product info as properties',
    scenario: 'Business',
    successCriteria:
      'Response should contain status code 200 and it should contain event name and all properties',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        method: 'POST',
        body: [
          {
            message: {
              type: 'track',
              sentAt: '2020-08-28T16:26:16.473Z',
              userId: 'userId',
              channel: 'web',
              context: {
                os: {
                  name: '',
                  version: '1.12.3',
                },
                app: {
                  name: 'RudderLabs JavaScript SDK',
                  build: '1.0.0',
                  version: '1.1.11',
                  namespace: 'com.rudderlabs.javascript',
                },
                traits: {
                  email: '<EMAIL>',
                },
                locale: 'en-US',
                device: {
                  token: 'token',
                  id: 'id',
                  type: 'ios',
                },
                screen: {
                  density: 2,
                },
                library: {
                  name: 'RudderLabs JavaScript SDK',
                  version: '1.1.11',
                },
                campaign: {},
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.16; rv:84.0) Gecko/20100101 Firefox/84.0',
              },
              rudderId: '9ovlz4kuew0wjcbwymj3vlhkngzixp9evf19',
              messageId: 'ev0qyvsclinoh4z4e1uz4d8pdhrnf17q0rjd',
              anonymousId: 'anonId',
              originalTimestamp: '2020-08-28T16:26:06.468Z',
              event: 'order completed',
              properties: {
                price: 45,
                quantity: 1,
                total: '1000',
                name: 'Shoes',
                orderId: 10000,
                product_id: 1234,
                campaignId: '123456',
                templateId: '1213458',
              },
            },
            metadata: baseMetadata,
            destination: {
              ID: '123',
              Name: 'iterable',
              DestinationDefinition: {
                ID: '123',
                Name: 'iterable',
                DisplayName: 'Iterable',
                Config: {},
              },
              Config: {
                apiKey: 'testApiKey',
                dataCenter: 'USDC',
                preferUserId: false,
                trackAllPages: true,
                trackNamedPages: false,
                mapToSingleEvent: false,
                trackCategorisedPages: false,
              },
              Enabled: true,
              WorkspaceID: '123',
              Transformations: [],
              RevisionID: 'default-revision',
              IsProcessorEnabled: true,
              IsConnectionEnabled: true,
            },
          },
        ],
      },
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              userId: '',
              method: 'POST',
              endpoint: 'https://api.iterable.com/api/commerce/trackPurchase',
              headers: {
                api_key: 'testApiKey',
                'Content-Type': 'application/json',
              },
              params: {},
              body: {
                JSON: {
                  dataFields: {
                    price: 45,
                    quantity: 1,
                    total: '1000',
                    name: 'Shoes',
                    orderId: 10000,
                    product_id: 1234,
                    campaignId: '123456',
                    templateId: '1213458',
                  },
                  user: {
                    email: '<EMAIL>',
                    dataFields: {
                      email: '<EMAIL>',
                    },
                    userId: 'userId',
                    preferUserId: false,
                    mergeNestedObjects: true,
                  },
                  id: '10000',
                  total: 1000,
                  campaignId: 123456,
                  templateId: 1213458,
                  createdAt: 1598631966468,
                  items: [
                    {
                      id: 1234,
                      name: 'Shoes',
                      price: 45,
                      quantity: 1,
                    },
                  ],
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
            },
            metadata: baseMetadata,
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    id: 'iterable-track-test-8',
    name: 'iterable',
    description: 'Track call without event name and userId',
    scenario: 'Business',
    successCriteria:
      'Response should contain status code 200 and it should contain event properties',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        method: 'POST',
        body: [
          {
            message: {
              anonymousId: 'anonId',
              type: 'track',
              context: {},
              properties: {
                subject: 'resume validate',
                sendtime: '2020-01-01',
                sendlocation: '<EMAIL>',
              },
              sentAt: '2020-08-28T16:26:16.473Z',
              originalTimestamp: '2020-08-28T16:26:06.468Z',
            },
            metadata: baseMetadata,
            destination: {
              ID: '123',
              Name: 'iterable',
              DestinationDefinition: {
                ID: '123',
                Name: 'iterable',
                DisplayName: 'Iterable',
                Config: {},
              },
              Config: {
                apiKey: 'testApiKey',
                dataCenter: 'USDC',
                preferUserId: false,
                trackAllPages: true,
                trackNamedPages: false,
                mapToSingleEvent: false,
                trackCategorisedPages: false,
              },
              Enabled: true,
              WorkspaceID: '123',
              Transformations: [],
              RevisionID: 'default-revision',
              IsProcessorEnabled: true,
              IsConnectionEnabled: true,
            },
          },
        ],
      },
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              userId: '',
              method: 'POST',
              endpoint: 'https://api.iterable.com/api/events/track',
              headers: {
                api_key: 'testApiKey',
                'Content-Type': 'application/json',
              },
              params: {},
              body: {
                JSON: {
                  userId: 'anonId',
                  createdAt: 1598631966468,
                  dataFields: {
                    subject: 'resume validate',
                    sendtime: '2020-01-01',
                    sendlocation: '<EMAIL>',
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
            },
            metadata: baseMetadata,
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    id: 'iterable-track-test-8',
    name: 'iterable',
    description: 'Track call without event name',
    scenario: 'Business',
    successCriteria:
      'Response should contain status code 200 and it should contain event properties',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        method: 'POST',
        body: [
          {
            message: {
              userId: 'userId',
              anonymousId: 'anonId',
              type: 'track',
              context: {},
              properties: {
                subject: 'resume validate',
                sendtime: '2020-01-01',
                sendlocation: '<EMAIL>',
              },
              sentAt: '2020-08-28T16:26:16.473Z',
              originalTimestamp: '2020-08-28T16:26:06.468Z',
            },
            metadata: baseMetadata,
            destination: {
              ID: '123',
              Name: 'iterable',
              DestinationDefinition: {
                ID: '123',
                Name: 'iterable',
                DisplayName: 'Iterable',
                Config: {},
              },
              Config: {
                apiKey: 'testApiKey',
                dataCenter: 'USDC',
                preferUserId: false,
                trackAllPages: true,
                trackNamedPages: false,
                mapToSingleEvent: false,
                trackCategorisedPages: false,
              },
              Enabled: true,
              WorkspaceID: '123',
              Transformations: [],
              RevisionID: 'default-revision',
              IsProcessorEnabled: true,
              IsConnectionEnabled: true,
            },
          },
        ],
      },
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              userId: '',
              method: 'POST',
              endpoint: 'https://api.iterable.com/api/events/track',
              headers: {
                api_key: 'testApiKey',
                'Content-Type': 'application/json',
              },
              params: {},
              body: {
                JSON: {
                  userId: 'userId',
                  createdAt: 1598631966468,
                  dataFields: {
                    subject: 'resume validate',
                    sendtime: '2020-01-01',
                    sendlocation: '<EMAIL>',
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
            },
            metadata: baseMetadata,
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    id: 'iterable-track-test-9',
    name: 'iterable',
    description: 'Track call to add event with user with EUDC dataCenter',
    scenario: 'Business',
    successCriteria:
      'Response should contain status code 200 and it should contain event properties, event name and endpointEUDC',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        method: 'POST',
        body: [
          {
            message: {
              anonymousId: 'anonId',
              event: 'Email Opened',
              type: 'track',
              context: {},
              properties: {
                subject: 'resume validate',
                sendtime: '2020-01-01',
                sendlocation: '<EMAIL>',
              },
              sentAt: '2020-08-28T16:26:16.473Z',
              originalTimestamp: '2020-08-28T16:26:06.468Z',
            },
            metadata: baseMetadata,
            destination: {
              ID: '123',
              Name: 'iterable',
              DestinationDefinition: {
                ID: '123',
                Name: 'iterable',
                DisplayName: 'Iterable',
                Config: {},
              },
              Config: {
                apiKey: 'testApiKey',
                dataCenter: 'EUDC',
                preferUserId: false,
                trackAllPages: true,
                trackNamedPages: false,
                mapToSingleEvent: false,
                trackCategorisedPages: false,
              },
              Enabled: true,
              WorkspaceID: '123',
              Transformations: [],
              RevisionID: 'default-revision',
              IsProcessorEnabled: true,
              IsConnectionEnabled: true,
            },
          },
        ],
      },
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              userId: '',
              method: 'POST',
              endpoint: 'https://api.eu.iterable.com/api/events/track',
              headers: {
                api_key: 'testApiKey',
                'Content-Type': 'application/json',
              },
              params: {},
              body: {
                JSON: {
                  userId: 'anonId',
                  createdAt: 1598631966468,
                  eventName: 'Email Opened',
                  dataFields: {
                    subject: 'resume validate',
                    sendtime: '2020-01-01',
                    sendlocation: '<EMAIL>',
                  },
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
            },
            metadata: baseMetadata,
            statusCode: 200,
          },
        ],
      },
    },
  },
];
