import os
import requests
import zipfile
import pickle
import numpy as np
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from openai import OpenAI
from qdrant_client import QdrantClient
from qdrant_client.models import VectorParams, Distance, PointStruct
import glob

# --- CONFIGURATION ---
ZIP_URL = "https://drive.google.com/uc?export=download&id=1B97CIvlIKXRsqwHVvw1MSJ2tJWUnX080"
ZIP_PATH = "embeddings.zip"
DATA_DIR = "data"
EMBEDDING_MODEL = "text-embedding-3-large"
RAG_VECTOR_SIZE = 1536
TOP_K = 20

# --- Download and extract embeddings.zip if needed ---
def download_file(url, dest):
    if not os.path.exists(dest):
        print(f"Downloading {dest} from {url} ...")
        r = requests.get(url)
        r.raise_for_status()
        with open(dest, 'wb') as f:
            f.write(r.content)
        print(f"Downloaded {dest}")

def extract_zip(zip_path, extract_to):
    if not os.path.exists(extract_to):
        os.makedirs(extract_to)
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_to)
        print(f"Extracted {zip_path} to {extract_to}")

download_file(ZIP_URL, ZIP_PATH)
extract_zip(ZIP_PATH, DATA_DIR)

# --- In-memory Qdrant setup ---
client = QdrantClient(":memory:")

# --- Load all .pkl files and create Qdrant collections ---
corpora = {"faq": [], "docs": []}
embeddings_map = {"faq": [], "docs": []}
ids_map = {"faq": [], "docs": []}

for pkl_path in glob.glob(os.path.join(DATA_DIR, "*.pkl")):
    name = os.path.splitext(os.path.basename(pkl_path))[0]
    with open(pkl_path, "rb") as f:
        data = pickle.load(f)
    if "faq" in name:
        target = "faq"
    else:
        target = "docs"
    corpora[target].extend(data["chunks"])
    embeddings_map[target].extend(data["embeddings"])
    # If you have ids, use them; else, just enumerate
    if "ids" in data:
        ids_map[target].extend(data["ids"])
    else:
        ids_map[target].extend(list(range(len(data["chunks"]))))
        
# Now, for each collection, create and upsert
for collection in ["faq", "docs"]:
    if not client.collection_exists(collection_name=collection):
        client.create_collection(
            collection_name=collection,
            vectors_config=VectorParams(size=RAG_VECTOR_SIZE, distance=Distance.COSINE)
        )
    points = [
        PointStruct(
            id=ids_map[collection][i],
            vector=embeddings_map[collection][i],
            payload={"text": corpora[collection][i]}
        )
        for i in range(len(corpora[collection]))
    ]
    client.upsert(collection_name=collection, points=points)

# --- FastAPI app ---
app = FastAPI()

class QueryRequest(BaseModel):
    query: str
    corpus: str

@app.post("/search")
async def search(req: QueryRequest):
    if req.corpus not in corpora:
        raise HTTPException(status_code=400, detail=f"Unknown corpus: {req.corpus}")
    client_oa = OpenAI()
    if not client_oa.api_key:
        client_oa.api_key = os.getenv("OPENAI_API_KEY")
    if not client_oa.api_key:
        raise HTTPException(status_code=500, detail="OpenAI API key not set")
    response = client_oa.embeddings.create(
        input=req.query,
        model=EMBEDDING_MODEL,
        dimensions=RAG_VECTOR_SIZE
    )
    query_emb = np.array(response.data[0].embedding, dtype="float32")
    # Qdrant expects a list of floats
    search_result = client.search(
        collection_name=req.corpus,
        query_vector=query_emb.tolist(),
        limit=TOP_K
    )
    results = [
        {"text": hit.payload["text"], "score": hit.score}
        for hit in search_result
    ]
    return {"results": results}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 