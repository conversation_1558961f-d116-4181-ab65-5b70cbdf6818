# RAG Search Service (FastAPI + QdrantDB in-memory)

This directory contains a standalone microservice for FAQ/doc retrieval, designed to be deployed on Replit (or similar cloud platforms). It is used by the main MCP as a remote retrieval API, so customers do not need an OpenAI API key or local embedding infrastructure.

## Features
- Downloads precomputed `.pkl` files (embeddings, docs) from Google Drive on startup if not present
- Loads embeddings and builds a FAISS index in memory
- Uses OpenAI API (key set as a Replit secret) to embed queries
- Exposes a `/search` endpoint for retrieval

## Dev testing
- Set your `OPENAI_API_KEY` as an environment variable
- Run the service, by running `python main.py`
- Update the `../src/constants.py` file, with the local endpoint (ex: `http://localhost:8000/search`)
- Test the endpoint. Example Query
```
curl -X POST https://<your-replit-username>.<repl-name>.repl.co/search \
  -H "Content-Type: application/json" \
  -d '{"query": "How do I use the FAQ system?"}'
``` 

## Deployment Steps
1. Ensure the google drive location in the `main.py` points to the latest embeddings zip
2. Set your `OPENAI_API_KEY` in the Replit Secrets panel
3. Click "Run" to start the service
4. Update the `../src/constants.py` file if required, with the endpoint.
