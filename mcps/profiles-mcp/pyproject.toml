[project]
name = "profiles-mcp"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10,<3.11"
dependencies = [
    "beautifulsoup4>=4.13.4",
    "langchain-community>=0.3.21",
    "mcp[cli]>=1.6.0",
    "numpy",
    "openai>=1.74.0",
    "pandas",
    "python-dotenv>=1.1.0",
    "pyyaml>=6.0.2",
    "qdrant-client>=1.13.3",
    "rudder-sdk-python>=2.1.4",
    "profiles-mlcorelib>=0.8.1",
    "kaleido==0.2.1",
    "requests>=2.31.0",
]
