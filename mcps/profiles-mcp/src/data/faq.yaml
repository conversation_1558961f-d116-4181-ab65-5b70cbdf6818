- question: I have installed Python3, yet when I install and execute pb it doesn't return anything on screen.
  answer: Try restarting your Terminal/Shell/PowerShell and try again. You can also try to find the location of your Python executable. PB would be installed where the executables embedded in other Python packages are installed.

- question: I am an existing user who updated to the new version and now I am unable to use the PB tool. On Windows, I get the error 'pb' is not recognized as an internal or external command, operable program or batch file.
  answer: |
    Execute the following commands to do a fresh install:
    pip3 uninstall profiles-rudderstack-bin
    pip3 uninstall profiles-rudderstack
    pip3 install profiles-rudderstack --no-cache-dir

- question: I am unable to download profile builder by running pip3 install profiles-rudderstack even though I have Python installed.
  answer: Firstly, make sure that Python3 is correctly installed. You can also try to substitute pip3 with pip and execute the install command.

- question: I am trying to execute the compile command by fetching a repo via GIT URL but getting this error - making git new public keys - ssh no key found
  answer: You need to add the OpenSSH private key to your siteconfig.yaml file. If you get the error could not find expected afterwards, try correcting the spacing in your siteconfig.yaml file.

- question: While trying to segregate identity stitching and feature table in separate model files, I am getting this error - mapping values are not allowed in this context
  answer: This is due to the spacing issue in siteconfig.yaml file. You may create a new project to compare the spacing. Also, make sure you haven't missed any keys.

- question: What are the prerequisites and Python environment requirements for running propensity models?
  answer: |
    Here are the key requirements for running propensity models:

    1. RudderStack Profiles Project Requirements:
       - Profiles version 0.18.0 or above
       - Supported data warehouses:
         - Snowflake
         - BigQuery
         - Redshift

    2. Python Version Requirements (Warehouse-specific):
       - For Redshift and BigQuery: Python 3.9.0 to 3.11.10
       - For Snowflake: Python ≥3.9.0 and <3.11.0

    3. Required Packages:
       - profiles-mlcorelib (version ≥0.7.2)
       - Must be installed in the same Python environment as profiles-rudderstack

    4. Project Configuration:
       Update your pb_project.yaml with:
       ```yaml
       python_requirements:
         - profiles_mlcorelib>=0.7.2
       ```

    Important: Always use an isolated Python virtual environment for your project to avoid dependency conflicts.

- question: I'm experiencing Python environment issues or version compatibility problems with Profile Builder. How can I resolve these?
  answer: |
    Here's a comprehensive guide to resolve Python environment issues:

    1. Supported Python Versions:
       - Profile Builder works best with Python 3.8 to 3.11
       - For installation issues, Python 3.8 to 3.10 are specifically recommended

       Warehouse-specific requirements:
       - Snowflake: Python ≥3.9.0 and <3.11.0
       - Redshift/BigQuery: Python 3.9.0 to 3.11.10

    2. Required Python Packages and Version Compatibility:
       Core packages:
       - profiles-rudderstack (main package, v0.18.0 or above for propensity models)
       - profiles-mlcorelib (version ≥0.7.2)
       - profiles-pycorelib

       Version compatibility requirements:
       - profiles-pycorelib 0.2.2 requires profiles-rudderstack >=0.10.5, <=0.10.7 (excluding 0.10.6)
       - Make sure all packages have matching compatible versions
       - If you encounter version conflicts, try downgrading to known compatible versions

    3. Environment Setup:
       - It's strongly recommended to use a Python virtual environment:
         ```
         python3 -m venv .venv
         source .venv/bin/activate
         ```
       - This keeps your Profiles dependencies isolated from other Python projects

    4. Project Configuration:
       - In your pb_project.yaml file, specify Python requirements:
         ```yaml
         python_requirements:
           packages:
             - profiles-rudderstack==0.18.0
             - profiles-mlcorelib>=0.7.2
             - profiles-pycorelib==0.2.2
         ```

    5. IDE Configuration:
       - If using VS Code or similar IDEs, set the Python interpreter:
         - Command Palette (Ctrl/Cmd + Shift + P)
         - "Python: Select Interpreter"
         - Choose the virtual environment's Python version
       - This ensures consistent behavior across your development environment

    If issues persist after following these steps, try performing a clean installation:
    ```
    pip3 uninstall profiles-rudderstack profiles-mlcorelib profiles-pycorelib
    pip3 install profiles-rudderstack==0.18.0 --no-cache-dir
    ```

    Note: Always check the latest documentation for the most up-to-date version compatibility information.

