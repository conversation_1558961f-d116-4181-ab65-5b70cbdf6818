"""
Vector Database Abstraction

This module provides abstract and concrete implementations for vector database operations
to store and query embeddings for documents and FAQ content.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional

import os
from qdrant_client import QdrantClient
from qdrant_client.models import VectorParams, Distance, PointStruct
import glob
import pickle
from pathlib import Path

from constants import RAG_MAX_RESULTS, RAG_VECTOR_SIZE

class VectorDb(ABC):
    """Abstract base class for vector database operations."""

    def __init__(self):
        """
        Initialize the vector database.
        """
        pass

    @abstractmethod
    def initialize_collection(self) -> None:
        """Initialize or recreate the vector collection."""
        pass

    @abstractmethod
    def add_documents(self, docs: List[str], embeddings: List[List[float]],
                     metadata: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        Add documents and their embeddings to the vector database.

        Args:
            docs: List of document texts
            embeddings: List of embedding vectors for each document
            metadata: Optional metadata for each document
        """
        pass

    @abstractmethod
    def query(self, query_vector: List[float], limit: int = 5,
              filter_condition: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        Search for similar documents using a query vector.

        Args:
            query_vector: The embedding vector of the query
            limit: Maximum number of results to return
            filter_condition: Optional filter to apply to the search

        Returns:
            List of matching documents with their metadata and scores
        """
        pass


class QdrantVectorDb(VectorDb):
    """Qdrant implementation of the VectorDb interface."""

    def __init__(self):
        """
        Initialize the Qdrant vector database.
        If using in-memory Qdrant, restore collections from cached docs if available.
        """
        super().__init__()

        url = os.getenv('QDRANT_BASE_URL')
        self._in_memory = url is None or url == ''
        if self._in_memory:
            self.client = QdrantClient(':memory:')
            self._restore_from_cache()
        else:
            self.client = QdrantClient(url=url)

    def _restore_from_cache(self):
        """
        Restore in-memory Qdrant collections from cached pickle files in the data directory.
        """
        # Assume this file is in src/utils, so go up one level to src, then look for data/
        base_dir = Path(__file__).parent.parent
        data_dir = base_dir / 'data'
        if not data_dir.exists():
            return
        # Find all .pkl files in data_dir
        for pkl_file in glob.glob(str(data_dir / '*.pkl')):
            try:
                with open(pkl_file, 'rb') as f:
                    doc_data = pickle.load(f)

                if 'faq' in Path(pkl_file).stem:
                    collection_name = 'faq'
                else:
                    collection_name = 'docs'

                if not self.client.collection_exists(collection_name=collection_name):
                    from constants import RAG_VECTOR_SIZE
                    self.client.create_collection(
                        collection_name=collection_name,
                        vectors_config=VectorParams(size=RAG_VECTOR_SIZE, distance=Distance.COSINE)
                    )

                self.add_documents(
                    collection_name=collection_name,
                    docs=doc_data['chunks'],
                    embeddings=doc_data['embeddings'],
                    ids=doc_data['ids']
                )
            except Exception as e:
                print(f"[QdrantVectorDb] Failed to restore {pkl_file}: {e}")

    def initialize_collection(self, collection_name: str, vector_size: int = RAG_VECTOR_SIZE) -> None:
        """
        Initialize or recreate the vector collection.

        Args:
            collection_name: Name of the vector collection
            vector_size: Dimensionality of the vector embeddings
        """
        if self.client.collection_exists(collection_name=collection_name):
            self.client.delete_collection(collection_name=collection_name)

        self.client.create_collection(
            collection_name=collection_name,
            vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
        )

    def add_documents(self, collection_name: str, docs: List[str], embeddings: List[List[float]],
                     ids: Optional[List[int]] = None) -> None:
        """
        Add documents and their embeddings to the Qdrant collection.

        Args:
            docs: List of document texts
            embeddings: List of embedding vectors for each document
            ids: Optional list of document IDs
        """
        if ids is None:
            ids = list(range(len(docs)))

        points = []
        for doc, embedding, id in zip(docs, embeddings, ids):
            payload = {"text": doc}

            points.append(
                PointStruct(
                    id=id,
                    vector=embedding,
                    payload=payload
                )
            )

        self.client.upsert(
            collection_name=collection_name,
            points=points
        )


    def query(self, collection_name: str, query_vector: List[float], limit: int = RAG_MAX_RESULTS) -> List[str]:
        """
        Query the vector database for similar documents.

        Args:
            collection_name: Name of the vector collection
            query_vector: The embedding vector of the query
            limit: Maximum number of results to return

        Returns:
            List of matching text documents
        """
        results = self.client.query_points(
            collection_name=collection_name,
            query=query_vector,
            limit=limit
        )
        return [point.payload['text'] for point in results.points]