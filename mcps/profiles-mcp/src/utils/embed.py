from abc import ABC, abstractmethod
from openai import OpenAI
import numpy as np

from constants import RAG_VECTOR_SIZE

class EmbedData(ABC):
    def __init__(self, model_name: str):
        self.model_name = model_name

    @abstractmethod
    def embed(self, doc: str) -> np.ndarray:
        pass

    @abstractmethod
    def embed_batch(self, docs: list[str], batch_size: int = 32) -> np.ndarray:
        pass

class OpenAIEmbedData(EmbedData):
    def __init__(self, model_name: str):
        self.model = OpenAI()
        self.model_name = model_name

    def embed(self, doc: str) -> np.ndarray:
        response = self.model.embeddings.create(input=doc, model=self.model_name, dimensions=RAG_VECTOR_SIZE)
        return np.array(response.data[0].embedding)

    def embed_batch(self, docs: list[str], batch_size: int = 32) -> np.ndarray:
        embeddings = []
        for doc in docs:
            embeddings.append(self.embed(doc))
        return np.array(embeddings)
