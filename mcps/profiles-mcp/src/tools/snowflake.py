from datetime import datetime, timedelta
import snowflake.snowpark
from snowflake.snowpark import Session
import os
from logger import setup_logger

logger = setup_logger(__name__)

class Snowflake:
    session: Session
    last_used: datetime

    def __init__(self):
        try:
            logger.info("Initializing Snowflake")
            self.session = self.create_snowflake_session()
            self.last_used = datetime.now()
        except Exception as e:
            logger.error(f"Error initializing Snowflake: {e}")

    def is_session_expired(self, timeout_hours: int = 1) -> bool:
        """Check if session hasn't been used for timeout_hours"""
        return datetime.now() - self.last_used > timedelta(hours=timeout_hours)

    def update_last_used(self):
        """Update the last used timestamp"""
        self.last_used = datetime.now()

    def create_snowflake_session(self) -> snowflake.snowpark.Session:
        """Create a new Snowflake session"""
        logger.info(f"Creating new Snowflake session for user: {os.getenv('SNOWFLAKE_USER')}")
        session = Session.builder.configs({
            "user": os.getenv('SNOWFLAKE_USER'),
            "password": os.getenv('SNOWFLAKE_PASSWORD'),
            "account": os.getenv('SNOWFLAKE_ACCOUNT'),
            "warehouse": os.getenv('SNOWFLAKE_WAREHOUSE'),
            "database": os.getenv('SNOWFLAKE_DATABASE'),
            "schema": os.getenv('SNOWFLAKE_SCHEMA'),
            "private_key": os.getenv('SNOWFLAKE_PRIVATE_KEY'),
            "private_key_file": os.getenv('SNOWFLAKE_PRIVATE_KEY_FILE')
        }).create()
        return session

    def ensure_valid_session(self) -> None:
        """Ensure we have a valid Snowflake session"""

        try:
            # Test session with a simple query
            # logger.debug("Testing Snowflake session validity...")
            self.session.sql("SELECT 1").collect()
            self.update_last_used()
        except Exception as e:
            # Session expired or invalid, create new one
            logger.warning(f"Session invalid or expired: {str(e)}")
            if self.session is not None:
                try:
                    self.session.close()
                except:
                    pass

            # Create new session
            logger.info("Creating new session due to expiration/invalidity")
            self.session = self.create_snowflake_session()
            self.update_last_used()

    def query(self, query: str) -> list[str]:
        """Query Snowflake and return results"""
        try:
            logger.info(f"Executing query: {query}")
            # Get valid session (handles reconnection if needed)
            self.ensure_valid_session()
            # Execute query
            result = self.session.sql(query)
            if query.lower().strip().startswith("select"):
                return result.toPandas()
            else:
                return result.collect()
        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}")
            raise Exception(f"Failed to execute query: {str(e)}")

    def raw_query(self, query: str) -> list[str]:
        """Query Snowflake and return results"""
        try:
            logger.info(f"Executing raw query: {query}")
            self.ensure_valid_session()
            return self.session.sql(query).collect()
        except Exception as e:
            message = f"Raw query execution failed: {str(e)}"
            logger.error(message)
            raise Exception(message)

    def input_table_suggestions(self, database: str, schemas: str) -> list[str]:
        default_tables = ['tracks', 'pages', 'identifies', 'screens']
        schema_list = [schema.strip() for schema in schemas.split(',')]
        suggestions = []

        def find_matching_tables(schema: str, table_names: list, candidates: list) -> list:
            """Find tables from the candidates list that exist in table_names (substring match)"""
            matches = []
            for candidate in candidates:
                for t in table_names:
                    if candidate.lower() in t.lower():
                        matches.append(f"{database}.{schema}.{t}")
            return matches

        for schema in schema_list:
            tables = self.raw_query(f"SHOW TABLES IN {database}.{schema}")
            table_names = [table['name'] for table in tables]

            # Substring match for default tables
            suggestions.extend(find_matching_tables(schema, table_names, default_tables))

            # For each table that matches 'tracks' as a substring, get event tables
            tracks_like_tables = [t for t in table_names if 'tracks' in t.lower()]
            for tracks_table in tracks_like_tables:
                try:
                    rows = self.raw_query(f"SELECT event, count(*) FROM {database}.{schema}.{tracks_table} group by event order by 2 desc limit 20")
                    event_names = [row['EVENT'] for row in rows]
                    # For each event, check if a table with that event name exists (substring match)
                    suggestions.extend(find_matching_tables(schema, table_names, event_names))
                except Exception:
                    logger.warning(f"Failed to query events from {schema}.{tracks_table}")

        return list(set(suggestions))  # Remove duplicates

    def describe_table(self, database: str, schema: str, table: str) -> list[str]:
        """Describe a table"""
        try:
            self.ensure_valid_session()
            results = self.raw_query(f"DESCRIBE TABLE {database}.{schema}.{table}")
            return [f"{row['name']}: {row['type']}" for row in results]
        except Exception as e:
            logger.error(f"Failed to describe table: {str(e)}")
            return f"Failed to describe table: {str(e)}"

    def get_row_count(self, table_name: str, count_column: str = "COUNT(*)", where_clause: str | None = None) -> int:
        """Helper function to get count of rows from a table"""
        query = f"SELECT {count_column} FROM {table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"

        try:
            self.ensure_valid_session()
            response = self.raw_query(query)
            return response[0][count_column] or 0
        except Exception as e:
            message = f"Failed to get row count for table {table_name} with where_clause {where_clause}: {str(e)}"
            logger.error(message)
            raise Exception(message)

    def eligible_user_evaluator(self, filter_sqls: list[str], label_table: str, label_column: str, entity_column: str, min_pos_rate: float = 0.10, max_pos_rate: float = 0.90, min_total_rows: int = 5000) -> dict:
        """Evaluate eligible user filters"""

        try:
            logger.info(f"Evaluating eligible user filters: {filter_sqls}")
            self.ensure_valid_session()

            total_positive_rows = self.get_row_count(label_table, f"COUNT(DISTINCT {entity_column})", f"{label_column}=1") or 1
            logger.info(f"Total positive rows: {total_positive_rows}")

            best_filter = None
            best_metrics = {
                "filter_sql": None,
                "recall": -1.0,
                "eligible_rows": -1,
                "positive_label_rows": -1,
                "negative_label_rows": -1,
                "positive_rate": -1.0,
            }


            for filter_sql in filter_sqls:
                filter_total_rows = self.get_row_count(label_table, f"COUNT(DISTINCT {entity_column})", filter_sql) or 1
                filter_positive_rows = self.get_row_count(label_table, f"COUNT(DISTINCT {entity_column})", f"{label_column}=1 AND {filter_sql}") or 1
                filter_negative_rows = filter_total_rows - filter_positive_rows

                positive_rate = filter_positive_rows / filter_total_rows
                recall = filter_positive_rows / total_positive_rows

                logger.info(f"Filter: {filter_sql}, Total rows: {filter_total_rows}, Positive rows: {filter_positive_rows}, Positive rate: {positive_rate}, Recall: {recall}")

                if positive_rate < min_pos_rate or positive_rate > max_pos_rate:
                    continue

                is_better = (
                    recall > best_metrics["recall"] or
                    (recall == best_metrics["recall"] and filter_total_rows > best_metrics["eligible_rows"])
                )

                if is_better:
                    best_metrics = {
                        "filter_sql": filter_sql,
                        "eligible_rows": filter_total_rows,
                        "positive_label_rows": filter_positive_rows,
                        "negative_label_rows": filter_negative_rows,
                        "positive_rate": round(positive_rate, 3),
                        "recall": round(recall, 3),
                    }
                    best_filter = filter_sql

            return {
                "best_filter": best_filter,
                "best_metrics": best_metrics,
            }

        except Exception as e:
            message = f"Failed to evaluate eligible user filters: {str(e)}"
            logger.error(message)
            raise Exception(message)

