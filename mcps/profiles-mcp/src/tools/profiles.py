import os
import yaml
import shutil
import subprocess
import json

from logger import setup_logger
from constants import PB_SITE_CONFIG_PATH


logger = setup_logger(__name__)


class ProfilesTools:
    def __init__(self):
        pass

    def get_existing_connections(self) -> list[str]:
        try:
            with open(PB_SITE_CONFIG_PATH, "r") as file:
                config = yaml.safe_load(file)
                connections = config["connections"]
                return list(connections.keys())
        except Exception as e:
            return f"Unable to read siteconfig.yaml file: {e}"

    def get_profiles_output_schema(self, pb_project_file_path: str) -> str:
        with open(pb_project_file_path, "r") as file:
            pb_project_config = yaml.safe_load(file)
            connection_name = pb_project_config["connection"]
        try:
            with open(PB_SITE_CONFIG_PATH, "r") as file:
                config = yaml.safe_load(file)
                connection_config = config["connections"][connection_name]
                output_schema = connection_config["outputs"][
                    connection_config["target"]
                ]["schema"]
                output_db = connection_config["outputs"][connection_config["target"]][
                    "dbname"
                ]
            return f"{output_db.upper()}.{output_schema.upper()}"
        except Exception as e:
            return f"Unable to read siteconfig.yaml file: {e}"

    def get_profiles_models_details(
        self, pb_project_file_path: str, pb_show_models_output_file_path: str
    ) -> dict:
        output_schema = self.get_profiles_output_schema(pb_project_file_path).upper()
        tables_info = {}
        with open(pb_show_models_output_file_path, "r") as file:
            pb_response = file.read()
        begin_idx = pb_response.find("Printing models") + len("Printing models")
        end_idx = pb_response.find("+------------------+")
        models_details = json.loads(pb_response[begin_idx:end_idx])
        for _, model_info in models_details.items():
            if model_info.get("model_type") == "feature_view":
                entity_name = model_info.get("model_path").split("/")[0]
                if entity_name not in tables_info:
                    tables_info[entity_name] = {"feature_views": [], "id_stitcher": ""}
                tables_info[entity_name]["feature_views"].append(
                    f"{output_schema}.{model_info['material_name'].upper()}"
                )
            elif model_info.get("model_type") == "id_stitcher":
                entity_name = model_info["model_path"].split("/")[0]
                if entity_name == "models":
                    continue
                if entity_name not in tables_info:
                    tables_info[entity_name] = {"feature_views": [], "id_stitcher": ""}
                id_stitcher_name = model_info["material_name"].upper()
                if (
                    "DEFAULT" not in id_stitcher_name
                    or tables_info[entity_name]["id_stitcher"] == ""
                ):
                    # Capture the id stitcher name if it's not captured yet. If it's already captured, overwrite if the original one was the default id-stitcher
                    # An underlying assumption here is that an entity can have max two id-stitchers, one with 'default' in the name and one without.
                    tables_info[entity_name][
                        "id_stitcher"
                    ] = f"{output_schema}.{id_stitcher_name}"
        response = {"output_schema": output_schema, "tables_info": tables_info}
        return response
        # else:
        #     return f"Error: {result.stderr}"

    def setup_new_profiles_project(self, project_path: str) -> dict:
        """
        Sets up a new profiles project in the specified directory using pip and venv.
        Steps:
        1. Ensure the project directory exists.
        2. Verify Python 3.10 is installed.
        3. Create a Python virtual environment.
        4. Install the profiles-rudderstack package using pip.
        5. Return a status dict with messages and errors.
        """
        messages = []
        errors = []

        abs_project_path = os.path.abspath(project_path)
        messages.append(f"Target project path: {abs_project_path}")

        def ensure_directory(path: str) -> bool:
            try:
                os.makedirs(path, exist_ok=True)
                messages.append(f"Project directory '{path}' ensured.")
                return True
            except Exception as e:
                errors.append(f"Error creating directory '{path}': {e}")
                return False

        def find_executable(name: str) -> str | None:
            executable_path = shutil.which(name)
            if not executable_path:
                errors.append(
                    f"`{name}` command not found. Please ensure it is installed and in your PATH."
                )
                return None
            messages.append(f"`{name}` command found at: {executable_path}.")
            return executable_path

        def check_python_version(executable_path: str) -> bool:
            try:
                result = subprocess.run(
                    [executable_path, "-c", "import sys; print(sys.version_info[:2])"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                version_str = result.stdout.strip()
                # Parse the output which looks like "(3, 10)"
                version_tuple = eval(version_str)
                if version_tuple[0] != 3 or version_tuple[1] != 10:
                    errors.append(f"Python version {version_tuple[0]}.{version_tuple[1]} detected. Python 3.10 is required.")
                    return False
                messages.append(f"Python 3.10 detected. Version requirement satisfied.")
                return True
            except Exception as e:
                errors.append(f"Failed to check Python version: {e}")
                return False

        def run_command(command: list[str], cwd: str, desc: str) -> bool:
            messages.append(f"Attempting: {desc}")
            messages.append(f"Executing: `{' '.join(command)}` in `{cwd}`")
            logger.info(f"Running command: {command}")
            current_env = os.environ.copy()
            try:
                process = subprocess.run(
                    command,
                    cwd=cwd,
                    check=True,
                    capture_output=True,
                    text=True,
                    env=current_env,
                )
                messages.append(f"Successfully executed: `{' '.join(command)}`.")
                if process.stdout.strip():
                    messages.append(f"Stdout:\n{process.stdout.strip()}")
                if process.stderr.strip():
                    messages.append(f"Stderr:\n{process.stderr.strip()}")
                return True
            except subprocess.CalledProcessError as e:
                logger.error(f"Command failed: {command}")
                errors.append(f"Error during: {desc}")
                errors.append(
                    f"Command `{' '.join(command)}` failed with exit code {e.returncode}."
                )
                if e.stdout and e.stdout.strip():
                    errors.append(f"Stdout:\n{e.stdout.strip()}")
                if e.stderr and e.stderr.strip():
                    errors.append(f"Stderr:\n{e.stderr.strip()}")
                return False
            except Exception as e:
                logger.error(f"An unexpected error occurred - {str(e)}")
                errors.append(f"An unexpected error occurred - {str(e)}")
                return False

        if not ensure_directory(abs_project_path):
            return {"status": "failure", "messages": messages, "errors": errors}

        python_executable = find_executable("python3") or find_executable("python")
        if not python_executable:
            return {"status": "failure", "messages": messages, "errors": errors}

        # Check Python version
        if not check_python_version(python_executable):
            return {"status": "failure", "messages": messages, "errors": errors}

        pip_executable = find_executable("pip3") or find_executable("pip")
        if not pip_executable:
            return {"status": "failure", "messages": messages, "errors": errors}

        venv_path = os.path.join(abs_project_path, ".venv")
        venv_bin_dir = os.path.join(venv_path, "bin" if os.name != "nt" else "Scripts")

        # Check for pip3 first, fall back to pip if pip3 is not available
        venv_pip3 = os.path.join(venv_bin_dir, "pip3")
        venv_pip = os.path.join(venv_bin_dir, "pip")
        venv_pip_to_use = venv_pip3 if os.path.exists(venv_pip3) else venv_pip
        venv_pb = os.path.join(venv_bin_dir, "pb")

        commands_to_execute = [
            {
                "cmd": [python_executable, "-m", "venv", ".venv"],
                "desc": "Create virtual environment (.venv)",
                "success_message": "Virtual environment '.venv' created",
                "skip_message": f"Virtual environment '.venv' already exists at '{venv_path}'",
                "pre_check": lambda: os.path.isdir(venv_path),
            },
            {
                "cmd": [venv_pip_to_use, "install", "profiles-rudderstack"],
                "desc": "Install 'profiles-rudderstack' package using pip",
                "success_message": "Package 'profiles-rudderstack' installed in the virtual environment",
                "pre_check": lambda: os.path.exists(venv_pb),
                "skip_message": f"Package 'profiles-rudderstack' already installed in the virtual environment at '{venv_pb}'",
            },
            {
                "cmd": [venv_pip_to_use, "install", "profiles-mlcorelib"],
                "desc": "Install 'profiles-mlcorelib' package using pip",
                "success_message": "Package 'profiles-mlcorelib' installed in the virtual environment",
                "pre_check": lambda: self._check_package_installed(venv_bin_dir, "profiles_mlcorelib"),
                "skip_message": f"Package 'profiles-mlcorelib' already installed in the virtual environment",
            },

        ]

        for item in commands_to_execute:
            pre_check = item.get("pre_check")
            logger.info(f"Pre-check: {pre_check}")
            if pre_check and pre_check():
                logger.info(f"Pre-check passed for command: {item['cmd']}")
                messages.append(
                    item.get("skip_message", "Step skipped due to pre-check.")
                )
                continue
            if not run_command(item["cmd"], abs_project_path, item["desc"]):
                return {"status": "failure", "messages": messages, "errors": errors}
            if "success_message" in item:
                messages.append(item["success_message"])

        # Create README.md file with activation instructions
        readme_path = os.path.join(abs_project_path, "README.md")
        readme_content = """# RudderStack Profiles Project

## Environment Setup

This project uses a Python virtual environment. To activate it, use one of the following methods:

### Using the standard Python venv activation:

```bash
source .venv/bin/activate
```

### Using uv (if installed):

```bash
uv activate .venv
```

Once activated, you can run Profiles commands with the `pb` CLI tool.

## Getting Started

After activating the environment, you can:

1. Initialize a new connection:
   ```
   pb init connection
   ```

2. Create your project configuration files (pb_project.yaml, inputs.yaml, models.yaml)

3. Run your profiles project:
   ```
   pb run
   ```

For more information, refer to the RudderStack Profiles documentation.
"""

        try:
            with open(readme_path, "w") as f:
                f.write(readme_content)
            messages.append("Created README.md with environment activation instructions")
        except Exception as e:
            errors.append(f"Error creating README.md file: {e}")

        return {
            "status": "success",
            "summary": "Project setup complete with pip and venv",
            "messages": messages,
            "errors": [],
        }

    def _check_package_installed(self, venv_bin_dir: str, package_name: str) -> bool:
        """Check if a Python package is installed in the virtual environment by trying to import it."""
        python_path = os.path.join(venv_bin_dir, "python")
        try:
            # Try to import the package in the virtual environment
            cmd = [python_path, "-c", f"import {package_name}"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode == 0
        except Exception:
            return False
