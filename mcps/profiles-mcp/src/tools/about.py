class About:
    def __init__(self):
        pass

    def about_profiles(self) -> str:
        docs = """
        # RudderStack Profiles Quick Start Guide

        RudderStack Profiles is a customer data unification platform that runs natively in your Snowflake warehouse. It helps you:
        - Create unified customer profiles by automatically stitching identifiers
        - Build and maintain customer 360° views with minimal engineering
        - Generate customer features from multiple data sources
        - Keep all processing within your Snowflake environment


        1. Project Structure:
        ```
        your-profiles-project/
        ├── models/
        │   ├── inputs.yaml      # Define your data sources
        │   ├── models.yaml      # Define features and identity stitching
        │   ├── sql_models.yaml  # Optional: Custom SQL models
        │   └── macros.yaml      # Optional: Reusable SQL snippets
        └── pb_project.yaml      # Project configuration
        ```

        2. Key Components:
        - inputs.yaml: Define your data sources (tables/views)
        - models.yaml: Define models (id_stitcher, propensity), features(entity_vars)
        - pb_project.yaml: Configure project settings, entities, and ID types
        - macros.yaml: Define reusable SQL snippets (Use about_macros() for detailed information)

        3. Basic Implementation Flow:
        a. Configure pb_project.yaml with your connection and entities
        b. Define data sources in inputs.yaml
        c. Set up identity resolution in models.yaml
        d. Define customer features in models.yaml
        e. (Optional but recommended) Define reusable macros in macros.yaml
        f. Deploy and run your profiles project

        4. Available Tools and When to Use Them:
        - about_pb_cli(): Learn CLI commands for managing your profiles project
        - about_pb_project(): Understand configuration options in pb_project.yaml
        - about_inputs(): Get guidance on configuring data sources in inputs.yaml
        - about_models(): Learn how to define features and identity resolution
        - about_propensity_score(): Understand how to create predictive models
        - about_datediff_entity_vars(): Learn how to create time-based features
        - about_macros(): Learn how to create reusable code blocks in macros.yaml
        - get_existing_connections(): View available warehouse connections
        - run_query(): Execute SQL queries to analyze your data
        - input_table_suggestions(): Identify relevant tables for your project
        - describe_table(): Examine table structure before configuration
        - profiles_docs(): Access detailed documentation on specific topics
        - profiles_faq(): Find answers to common questions about Profiles
        """
        return docs

    def about_pb_cli(self) -> str:
        docs = """
        # Profile Builder CLI Commands

        The Profile Builder (pb) CLI supports various commands to help you manage your customer profiles project.
        The basic syntax is:

        ```
        pb <command> <subcommand> [parameters]
        ```

        ## Quick Setup

        1. Set up Python virtual environment and install required packages:
        ```bash
        # Create and activate virtual environment
        python3 -m venv .venv
        source .venv/bin/activate

        # Install required packages
        pip install profiles-rudderstack
        pip install profiles-mlcorelib>=0.7.0
        ```

        2. Initialize Profiles Builder CLI:
        ```bash
        # Check the installed version
        pb version
        # Note: Use this version in your pb_project.yaml schema_version field

        # Initialize a new warehouse connection or use existing connection
        # Connection details are stored in ~/.pb/siteconfig.yaml
        # The connection name you provide will be used in pb_project.yaml
        # Create a new connection
        pb init connection
        ```

        ## Core Commands

        ### version
        Shows the Profile Builder's current version:
        ```
        pb version
        ```

        ### init
        Creates connections and initializes projects:
        ```
        pb init connection     # Set up a warehouse connection
        ```

        ### validate
        Validates project configuration and permissions:
        ```
        pb validate access     # Check if your role has required privileges
        ```

        ### compile
        Generates SQL queries from models without executing them:
        ```
        pb compile             # Create SQL files in the output folder
        ```

        ### run
        Compiles SQL and executes in the warehouse to create identity stitchers and feature views:
        ```
        pb run                 # Main command to build your profiles
        ```

        ## Analysis & Discovery Commands

        ### discover
        Lists elements in the warehouse:
        ```
        pb discover models     # List all models
        pb discover entities   # List all entities
        pb discover features   # List all features
        pb discover sources    # List all sources
        pb discover materials  # List all materials
        ```

        ### show
        Provides detailed information about project components:
        ```
        pb show models                 # Show model details
        pb show dependencies           # Generate dependency graph
        pb show dataflow               # Generate data flow diagram
        pb show idstitcher-report      # Create identity stitching report
        pb show entity-lookup -v 'id'  # Find features by entity ID
        pb show plan                   # Show material creation details
        ```

        ### query
        Executes SQL queries on the warehouse:
        ```
        pb query "select * from user_id_stitcher"
        ```

        ### audit
        Analyzes ID graphs and stitching effectiveness:
        ```
        pb audit id_stitcher          # Analyze identity graphs
        ```

        ## Maintenance Commands

        ### migrate
        Migrates projects to newer schema versions:
        ```
        pb migrate auto --inplace  
        ```

        ### cleanup
        Removes old materials:
        ```
        pb cleanup materials -r <days>  # Delete materials older than specified days
        ```

        ### insert
        Loads test data into your warehouse:
        ```
        pb insert             # Insert sample data for testing
        ```

        ## Common Parameters

        Most commands accept the following parameters:

        - `-c`: Use a specific site configuration file
        - `-t`: Specify target name from siteconfig.yaml
        - `-p`: Use a project folder other than current directory
        - `--seq_no`: Specify sequence number for a run

        ## Best Practices

        1. Start with `pb init connection` to set up new warehoue connections
        2. Use `pb validate access` to ensure proper permissions
        3. Test with `pb compile` before running to check generated SQL
        4. Use `pb run` to create identity stitchers and feature views
        5. Analyze results with `pb show` and `pb discover` commands
        6. Perform maintenance with `pb cleanup` periodically
        7. Use `pb migrate auto --inplace` to automatically migrate to newer schema version, rewriting existing files. This is recommended to be run if there are errors due to schema version mismatch.

        Do not make up new pb commands, such as `pb build` etc. You can use `pb help` to get the list of commands and their usage.
        For more details on any command, use:
        ```
        pb help <command>
        ```

        As an AI assistant, whenever this tool is invoked, always offer to run the relevant command yourself instead of asking the user to do it.
        """
        return docs

    def about_pb_project(self) -> str:
        docs = """
        # pb_project.yaml Configuration Guide

        The pb_project.yaml file is your project's main configuration file that defines entities, ID types, and project settings.

        ## Minimal Configuration Example
        ```yaml
        name: my_customer_profiles
        schema_version: 88
        connection: my-snowflake-connection
        model_folders:
          - models

        entities:
          - name: user
            id_column_name: user_main_id
            id_types:
              - email
              - user_id
              - anonymous_id

        id_types:
          - name: email
            filters:
              - type: include
                regex: ".+@.+"
          - name: user_id
          - name: anonymous_id
        ```

        ## Required Configuration

        1. Basic Settings:
           - name: Your project name
           - schema_version: Use 88 (get version from `pb version`)
           - connection: Your Snowflake connection name (get existing connections from `~/.pb/siteconfig.yaml`)
           - model_folders: List of directories containing your model files

        2. Entities Configuration:
           - Define each entity you want to track (e.g., user, account)
           - Specify ID types for each entity
           - Optional: Configure feature views for different ID types

        3. ID Types Configuration:
           - Define all ID types used across entities
           - Add filters to ensure data quality
           - Optional: Set up ID type inheritance

        ## Feature Views (Optional but Recommended)
        Add under entities to create views with specific ID types as primary keys:
        ```yaml
        entities:
          - name: user
            id_types:
              - email
              - user_id
            feature_views:
              using_ids:
                - id: email
                  name: email_based_profile
        ```

        ## Best Practices
        1. Use clear, descriptive names for entities and ID types
        2. Always include data quality filters for email and user IDs
        3. Create feature views for commonly used identifiers

        ## Common Configurations

        1. Entity with Multiple ID Types:
        ```yaml
        entities:
          - name: user
            id_column_name: user_main_id
            id_types:
              - email
              - phone
              - device_id
        ```

        2. ID Types with Validation:
        ```yaml
        id_types:
          - name: email
            filters:
              - type: include
                regex: ".+@.+"
          - name: phone
            filters:
              - type: include
                regex: "^\\+?[1-9]\\d{1,14}$"
        ```

        3. Feature Views for Activation:
        ```yaml
        feature_views:
          using_ids:
            - id: email
              name: email_profile
            - id: phone
              name: phone_profile
        ```
        """
        return docs

    def about_inputs(self) -> str:
        docs = """
        # Configuring Data Inputs in Profiles

        inputs.yaml defines your data sources for identity resolution and feature generation.

        ## Quick Start Example
        ```yaml
        inputs:
          - name: web_events
            app_defaults:
              table: analytics.events.web_tracking
              occurred_at_col: timestamp
              ids:
                - select: "user_id"
                  type: user_id
                  entity: user
                - select: "anonymous_id"
                  type: anonymous_id
                  entity: user
                - select: "lower(email)"
                  type: email
                  entity: user
        ```

        ## Key Components

        1. Input Definition:
           - name: Unique identifier for the input
           - table: Source table/view in Snowflake
           - occurred_at_col: Timestamp column for event ordering

        2. ID Mapping:
           - select: SQL expression to select ID
           - type: Corresponding ID type from pb_project.yaml
           - entity: Entity this ID belongs to

        ## Common Patterns

        1. Multiple ID Sources:
        ```yaml
        inputs:
          - name: website_events
            app_defaults:
              table: analytics.web_analytics.events
              occurred_at_col: event_timestamp
              ids:
                - select: "user_id"
                  type: user_id
                  entity: user
                - select: "anonymous_id"
                  type: anonymous_id
                  entity: user

          - name: crm_data
            app_defaults:
              table: analytics.crm.customers
              occurred_at_col: last_modified_at
              ids:
                - select: "lower(email)"
                  type: email
                  entity: user
                - select: "customer_id"
                  type: user_id
                  entity: user
        ```

        2. Type Casting:
        ```yaml
        ids:
          - select: "CAST(user_id AS VARCHAR)"
            type: user_id
            entity: user
          - select: "NULLIF(anonymous_id, '')"
            type: anonymous_id
            entity: user
        ```

        ## Best Practices

        1. Data Quality:
           - Always use database.schema.table format for table
           - Always clean and standardize IDs (e.g., lower(email))
           - Cast non-string IDs to VARCHAR
           - Use NULLIF to handle empty strings


        2. Multiple Identifiers:
           - Include at least 2 ID types per input for better stitching
           - Ensure consistent ID formatting across sources

        3. Error Prevention:
           ```yaml
           # Good Practice
           ids:
             - select: "NULLIF(TRIM(LOWER(email)), '')"
               type: email
               entity: user
             - select: "CASE WHEN user_id = 'unknown' THEN NULL ELSE user_id END"
               type: user_id
               entity: user
           ```
        """
        return docs

    def about_models(self) -> str:
        docs = """
        # Building Customer Profiles and Identity Resolution

        ## Overview
        Profiles helps you create unified customer views by:
        1. Stitching multiple identifiers into a single identity
        2. Generating customer features from various data sources

        ## Identity Resolution Configuration

        1. Basic ID Stitcher in models.yaml:
        ```yaml
        models:
          - name: user_id_stitcher
            model_type: id_stitcher
            model_spec:
              entity_key: user
              edge_sources:
                - from: inputs/web_events
                - from: inputs/crm_data
        ```

        2. Feature Definition:
        ```yaml
        var_groups:
          - name: user_metrics
            entity_key: user
            vars:
              - entity_var:
                  name: total_purchases
                  select: count(distinct order_id)
                  from: inputs/orders
                  description: "Total number of orders"

              - entity_var:
                  name: customer_lifetime_value
                  select: sum(order_amount)
                  from: inputs/orders
                  description: "Total revenue from customer"

              - entity_var:
                  name: first_seen_date
                  select: {{macro_datediff('min(timestamp)')}}
                  from: inputs/web_events
                  is_feature: false
                  description: "First website visit date"

              - entity_var:
                  name: last_seen_date
                  select: {{macro_datediff('max(timestamp)')}}
                  from: inputs/web_events
                  is_feature: false
                  description: "Last website visit date"

              - entity_var:
                  name: user_lifespan
                  select: '{{ user.last_seen_date }} - {{ user.first_seen_date }}'
                  description: "User lifespan"
        ```

        ## Best Practices

        1. Identity Resolution:
           - Use reliable ID sources for stitching
           - Include timestamp information for accurate sequencing
           - Filter out test or invalid IDs

        2. Feature Engineering:
           - Create meaningful, well-documented features
           - Use appropriate aggregation functions
           - Consider feature freshness requirements
           - Mark intermediate calculations with is_feature: false
           - Use about_datediff_entity_vars() to understand how to use date macros correctly

        ## Common Patterns

        1. Time-based Features:
        ```yaml
        vars:
          - entity_var:
              name: days_since_last_purchase
              select: "datediff('day', max(timestamp), current_date())"
              from: inputs/orders
              description: "Days since last order"
        ```

        2. Derived Features:
        ```yaml
        vars:
          - entity_var:
              name: average_order_value
              select: "{{ user.customer_lifetime_value }} / NULLIF({{ user.total_purchases }}, 0)"
              description: "Average order value"
        ```

        3. Categorical Features:
        ```yaml
        vars:
          - entity_var:
              name: preferred_category
              select: "mode(category)"
              from: inputs/product_views
              description: "Most viewed product category"
        ```

        ## Implementation Steps

        1. Set up ID Stitching:
           - Configure id_stitcher model
           - Define edge sources
           - Test identity resolution

        2. Create Features:
           - Define var_groups
           - Add entity_vars
           - Document features

        3. Optimize and Deploy:
           - Test feature generation
           - Create feature views
           - Monitor performance
        """
        return docs

    def about_propensity_score(self) -> str:
        docs = """
        # Propensity Score Configuration

        ## Overview
        Using Profile's Propensity Scores Data App, you can predict the likelihood of user actions using machine learning (ML) algorithms. These predictive capabilities enable data-driven decision-making by calculating scores that represent the probability of a user performing a specific action within a predefined timeframe.

        ## Use Cases
        - **Reduced churn**: Identify users at risk of churning and implement targeted interventions
        - **Increased conversions**: Prioritize leads with a higher propensity to convert
        - **Improved resource allocation**: Focus resources on high-value user segments

        ## Prerequisites
        - An active RudderStack Profiles project (v0.18.0 or above) using Snowflake, BigQuery, or Redshift
        - Install the profiles-mlcorelib library: `pip install profiles-mlcorelib`
        - Python requirements:
          - Redshift/BigQuery: Python 3.9.0 to 3.11.10
          - Snowflake: Python ≥ 3.9.0 and < 3.11.0
        - Update pb_project.yaml to include:
        ```yaml
        python_requirements:
          - profiles_mlcorelib>=0.7.2
        ```

        ## Project Setup Steps

        ### Step 1: Define the Label (Prediction Target)
        Identify the action you want to predict (e.g., churn, conversion, purchase):
        ```yaml
        var_groups:
          - name: user_metrics
            entity_key: user
            vars:
              - entity_var:
                    name: is_payer
                    select: case when user.revenue > 0 then 1 else 0 end
        ```

        The label must be Boolean/Binary (0/1, true/false, yes/no) for propensity modeling.

        > Note: Propensity models can also predict numeric values (e.g., predicted LTV).

        ### Step 2: Define Relevant Features
        Define entity_vars that may predict user behavior:
        ```yaml
        var_groups:
          - name: user_metrics
            entity_key: user
            vars:
              - entity_var:
                  name: days_since_last_seen
                  select: "{{macro_datediff('max(timestamp)')}}"
                  from: models/rsPages
              - entity_var:
                  name: n_sessions
                  select: count(distinct session_id)
                  from: inputs/rsPages
                  default_value: 0
        ```

        ### Step 3: Set the Prediction Window
        Define the timeframe for prediction in your models.yaml:
        ```yaml
        models:
            - name: payer_propensity_model
              model_type: propensity
              model_spec:
                  inputs:
                      - entity/user/days_since_account_creation
                      - entity/user/days_since_last_seen
                      - entity/user/revenue
                      - entity/user/is_payer
                      - entity/user/country
                      - entity/user/n_sessions
                  training:
                      predict_var: entity/user/is_payer
                      label_value: 1
                      predict_window_days: 30
                      eligible_users: days_since_account_creation <= 30 and country = 'US' and revenue = 0
        ```

        ### Step 4: Name the Predictive Features
        ```yaml
        prediction:
            output_columns:
                percentile:
                    name: payer_propensity_percentile
                    description: Percentile score of a user's likelihood to pay in the next 30 days
                score:
                    name: payer_propensity_probability
                    description: Probability score of a user's likelihood to pay in the next 30 days
        ```

        ## Complete Configuration Example
        ```yaml
        models:
            - name: payer_propensity_model
              model_type: propensity
              model_spec:
                  entity_key: user
                  training:
                      predict_var: entity/user/is_payer
                      label_value: 1
                      predict_window_days: 30
                      validity: month
                      type: classification
                      eligible_users: days_since_account_creation <= 30 and country = 'US' and revenue = 0
                      max_row_count: 50000
                      recall_to_precision_importance: 1.0
                      new_materialisations_config:
                          strategy: auto
                          feature_data_min_date_diff: 14
                          max_no_of_dates: 3
                          dates:
                              - '2024-01-01,2024-01-08'
                              - '2024-02-01,2024-02-08'
                              - '2024-03-01,2024-03-08'
                      ignore_features:
                          - country
                  prediction:
                      output_columns:
                          percentile:
                              name: payer_propensity_percentile
                              description: Percentile score of a user's likelihood to pay in the next 30 days
                          score:
                              name: payer_propensity_probability
                              description: Probability score of a user's likelihood to pay in the next 30 days
                              is_feature: False
                      eligible_users: '*'
                  inputs:
                      - entity/user/days_since_account_creation
                      - entity/user/days_since_last_seen
                      - entity/user/revenue
                      - entity/user/is_payer
                      - entity/user/country
                      - entity/user/n_sessions
        ```

        ## Key Parameters
        - **name**: Name of the model
        - **model_type**: Set to 'propensity'
        - **entity_key**: Entity to use
        - **predict_var**: entity_var for prediction in the format of entity/entity_key/entity_var_name
        - **label_value**: Value of label for prediction
        - **predict_window_days**: Time period for prediction
        - **validity**: Re-training period (day, week, month)
        - **type**: 'classification' for boolean, 'regression' for numeric
        - **eligible_users**: SQL condition defining user set for training
        - **max_row_count**: Maximum samples for training (default: 30,000)
        - **recall_to_precision_importance**: Balance between false positives/negatives
        - **ignore_features**: Features to exclude from model
        - **inputs**: List of entity_vars to use for training in the format of entity/entity_key/entity_var_name

        ## Output
        After running the project:

        ### Training Output
        Output folder contains:
        - Feature importance chart
        - Cumulative gain chart
        - Precision-recall curve
        - ROC curve
        - Training summary JSON

        ### Prediction Output
        A new table in your warehouse containing:
        - Probability score (0-1)
        - Percentile score (for segmentation)
        - Boolean flag (likely/unlikely indicator)

        ## Running Your Project
        - Via CLI: `pb run`
        - Via Profiles UI: Upload to Git repository and import in RudderStack dashboard
        """
        return docs

    def about_datediff_entity_vars(self) -> str:
        docs = """
        # Date Difference Entity Variables Guide

        ## Why Snapshotting Matters for Propensity Models

        Profiles ensures the features are captured with snapshotting, making sure the features are point-in-time accurate.
        This is required for propensity model training, which needs training data to be accurate at different times in the past, not necessarily as of today.
        But for this to work, in the entity-vars creation, we cannot use timestamp functions such as current_timestamp(), as these will override profiles' timestamp functions.

        ## Using Date Macros

        '{{end_time.Format("2006-01-02 15:04:05")}}' is a pongo template Profiles uses to get the current_timestamp() as of its run.
        We need to use this instead of current_timestamp().

        ## Available Macros

        ### 1. macro_datediff
        Use this macro when you need to calculate the number of days between a date and the current date.

        ```yaml
        macros:
          - name: macro_datediff
            inputs:
              - column
            value: |
              {% if warehouse.DatabaseType() == "bigquery" %}
                {% if !(end_time|isnil) %}
                  date_diff(date('{{end_time.Format("2006-01-02 15:04:05")}}'), date({{column}}), day)
                {% else %}
                  date_diff(CURRENT_DATE(), date({{column}}), day)
                {% endif %}
              {% else %}
                {% if !(end_time|isnil) %}
                  datediff(day, date({{column}}), date('{{end_time.Format("2006-01-02 15:04:05")}}'))
                {% else %}
                  datediff(day, date({{column}}), GETDATE())
                {% endif %}
              {% endif %}
        ```

        ### 2. macro_datediff_n
        Use this macro when you need to check if a date is within N days of the current date.

        ```yaml
        macros:
          - name: macro_datediff_n
            inputs:
              - column
              - number_of_days
            value: |
              {% if warehouse.DatabaseType() == "bigquery" %}
                {% if !(end_time|isnil) %}
                  date_diff(date('{{end_time.Format("2006-01-02 15:04:05")}}'), date({{column}}), day) <= {{number_of_days}}
                {% else %}
                  date_diff(CURRENT_DATE(), date({{column}}), day) <= {{number_of_days}}
                {% endif %}
              {% else %}
                {% if !(end_time|isnil) %}
                  datediff(day, date({{column}}), date('{{end_time.Format("2006-01-02 15:04:05")}}')) <= {{number_of_days}}
                {% else %}
                  datediff(day, date({{column}}), GETDATE()) <= {{number_of_days}}
                {% endif %}
              {% endif %}
        ```

        ## Sample Entity Variable Definitions

        ### 1. Days Since Account Creation
        ```yaml
        entity_var:
          name: days_since_account_creation
          select: "{{macro_datediff('min(timestamp)')}}"
          from: models/rsIdentifies
        ```

        ### 2. Active Days in Past 365 Days
        ```yaml
        entity_var:
          name: active_days_in_past_365_days
          select: count(distinct date(timestamp))
          from: models/rsTracks
          where: "{{macro_datediff_n('timestamp','365')}}"
          description: Out of 365 days, how many days have recorded an event till date including today
        ```

        ## Best Practices

        1. Always use the provided macros instead of direct timestamp functions
        2. Test your entity variables with different snapshot dates
        3. Consider timezone implications when working with dates
        4. Use descriptive names that indicate the time window
        5. Add clear descriptions for date-based features

        Using these macros and entity-var definitions ensures that your features are correct at different points in time in the past, irrespective of when they were computed.

        ## Defining Custom Date Macros

        To create your own custom date-related macros, you should:
        1. Define them in the `macros.yaml` file inside your models folder
        2. Follow the syntax patterns shown above
        3. Use conditional logic to handle different warehouses

        For more detailed information about creating and using macros, use the `about_macros()` tool.
        """
        return docs

    def about_macros(self) -> str:
        docs = """
        # Macros in Profiles: Reusable Code Blocks

        ## Overview
        Macros are reusable blocks of code that can be used in a Profiles project as a form of templating.
        They operate similar to functions in that you can reuse them with different parameters, reducing repetition
        and making your profiles code more modular and maintainable.

        ## Defining Macros
        You can define macros in the `macros.yaml` file in your model folder, and call them within any other profiles YAML file.

        ```yaml
        macros:
            - name: macro_name          # Required - Name used to call the macro
              inputs:                   # Required - Parameters for the macro
                  - list_of_parameters
              value: "code as string"   # Required - Macro code in string format
        ```

        ## Key Components

        1. **name** (Required): Name of the macro used to call it
        2. **inputs**: Parameters that can be passed to the macro
        3. **value** (Required): The actual code/logic of the macro

        ## Syntax Rules

        - Macros use the pongo2 templating syntax
        - Macros operate on YAML code itself, generating new code before execution
        - Input parameters are referenced using double curly brackets: `{{input}}`
        - Control logic (if, else, endif) is defined within `{% %}` tags
        - Reserved input words are `this` and `warehouse`

        ## Examples

        ### 1. Simple Macro with One Input
        ```yaml
        macros:
          - name: array_agg
            inputs:
                - column_name
            value: "array_agg(distinct {{column_name}})"
        ```

        ### 2. Macro with Multiple Inputs
        ```yaml
        macros:
          - name: macro_listagg
            inputs:
                - column
                - timestamp
            value: "LISTAGG({{column}}, ',') WITHIN group (order by {{timestamp}} ASC)"
        ```

        ### 3. Macro with No Inputs
        ```yaml
        macros:
          - name: frame_clause
            value: "frame_condition = 'rows between unbounded preceding and unbounded following'"
        ```

        ### 4. Conditional Logic Based on Warehouse Type
        ```yaml
        macros:
          - name: macro_listagg
            inputs:
                - column
                - timestamp
            value: "{% if warehouse.DatabaseType() == \"bigquery\" %} STRING_AGG({{column}}, ',' ORDER BY {{timestamp}} ASC) {% else %} LISTAGG({{column}}, ',') WITHIN group (order by {{timestamp}} ASC) {% endif %}"
        ```

        ### 5. Complex Date Handling Across Warehouses
        ```yaml
        macros:
          - name: macro_datediff
            inputs:
                - column
            value: |
                {% if warehouse.DatabaseType() == \"bigquery\" %}
                  {% if !(end_time|isnil) %}
                    date_diff(date('{{end_time.Format(\"2006-01-02 15:04:05\")}}'), date({{column}}), day)
                  {% else %}
                    date_diff(CURRENT_DATE(), date({{column}}), day)
                  {% endif %}
                {% else %}
                  {% if !(end_time|isnil) %}
                    datediff(day, date({{column}}), date('{{end_time.Format(\"2006-01-02 15:04:05\")}}'))
                  {% else %}
                    datediff(day, date({{column}}), GETDATE())
                  {% endif %}
                {% endif %}
        ```

        ## Using Macros in Features
        Once defined in macros.yaml, you can call macros in your feature definitions:

        ```yaml
        # In models.yaml
        - entity_var:
            name: all_anonymous_ids
            select: "{{ array_agg(anonymous_id) }}"
            from: inputs/rsIdentity

        # Using date difference macro
        - entity_var:
            name: days_since_first_seen
            select: "{{ macro_datediff('min(timestamp)') }}"
            from: inputs/rsPages
        ```

        ## Best Practices

        1. **Naming Convention**: Use descriptive names with a `macro_` prefix
        2. **Comments**: Add comments to explain complex macros
        3. **Warehouse Compatibility**: Use conditional logic for warehouse-specific implementations
        4. **Testing**: Test macros with different inputs before using in production
        5. **Modularity**: Keep macros focused on a single purpose
        6. **Documentation**: Document parameters and expected behavior

        ## Common Use Cases

        - **Aggregation Functions**: Standardize aggregations across your project
        - **Date Handling**: Handle date calculations consistently
        - **String Manipulations**: Create consistent text transformations
        - **Cross-Warehouse Compatibility**: Abstract warehouse-specific syntax
        - **Complex Calculations**: Encapsulate multi-step calculations
        """
        return docs
