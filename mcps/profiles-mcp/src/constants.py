from pathlib import Path

EMBEDDING_MODEL = "text-embedding-3-large"
RAG_VECTOR_SIZE = 1536
RAG_CHUNK_SIZE = 1000
RAG_CHUNK_OVERLAP = 200
RAG_MAX_RESULTS = 20


ANALYTICS_WRITE_KEY = "2xL75MYRl00bI88EqinCq5T7RfO"
ANALYTICS_DATA_PLANE_URL = "https://rudderstacqiqh.dataplane.rudderstack.com"

RETRIEVAL_API_URL = "https://rudder-profiles-rag.replit.app"

PB_CONFIG_DIR = Path.home() / ".pb"
PB_PREFERENCES_FILE = "preferences.yaml"
PB_PREFERENCES_PATH = PB_CONFIG_DIR / PB_PREFERENCES_FILE
PB_SITE_CONFIG_FILE = "siteconfig.yaml"
PB_SITE_CONFIG_PATH = PB_CONFIG_DIR / PB_SITE_CONFIG_FILE
