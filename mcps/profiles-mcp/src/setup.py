"""
RudderStack Documentation Embedding Generator

This script processes documentation and FAQ content, generates embeddings,
and stores them in vector databases for retrieval.
"""

import os
import pickle
import logging
from typing import Dict, List, Any, Optional

from dotenv import load_dotenv
from langchain.schema import Document
from langchain_community.document_loaders import WebBaseLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter
from pathlib import Path

from utils.embed import OpenAIEmbedData
from utils.vectordb import QdrantVectorDb

from constants import EMBEDDING_MODEL, RAG_VECTOR_SIZE, RAG_CHUNK_SIZE, RAG_CHUNK_OVERLAP

# Load environment variables
load_dotenv()

logging.basicConfig(
    level=os.getenv('LOG_LEVEL', 'INFO'),
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration constants
SCRIPT_DIR = os.path.dirname(__file__)
DATA_DIR = 'data'
FAQ_COLLECTION = 'faq'
DOCS_COLLECTION = 'docs'
BASE_URL = "https://www.rudderstack.com/docs/"

# RudderStack documentation URLs to process
DOC_URLS = [
    "https://www.rudderstack.com/docs/profiles/overview/",
    "https://www.rudderstack.com/docs/profiles/overview/how-profiles-works/",
    "https://www.rudderstack.com/docs/profiles/overview/quickstart/",
    "https://www.rudderstack.com/docs/profiles/concepts/entities/",
    "https://www.rudderstack.com/docs/profiles/concepts/id/",
    "https://www.rudderstack.com/docs/profiles/concepts/identity-graph/",
    "https://www.rudderstack.com/docs/profiles/concepts/inputs/",
    "https://www.rudderstack.com/docs/profiles/concepts/features/",
    "https://www.rudderstack.com/docs/profiles/concepts/feature-views/",
    "https://www.rudderstack.com/docs/profiles/concepts/cohort/",
    "https://www.rudderstack.com/docs/profiles/concepts/timegrains/",
    "https://www.rudderstack.com/docs/profiles/concepts/sql-models/",
    "https://www.rudderstack.com/docs/profiles/concepts/cleanup/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/project-structure/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/site-configuration-file/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/pb-project-yaml/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/pb-project-yaml/entities/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/pb-project-yaml/id-types/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/profiles-yaml/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/profiles-yaml/id-stitcher/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/profiles-yaml/var-groups/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/profiles-yaml/var-groups/input-var/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/profiles-yaml/var-groups/entity-var/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/profiles-yaml/var-groups/window-functions/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/profiles-yaml/cohorts/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/inputs-yaml/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/inputs-yaml/identifiers/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/inputs-yaml/s3-bucket-input/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/sql-model-yaml/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/sql-model-yaml/sql-model-config/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/macros-yaml/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/optimizations/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/run-project/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/warehouse-output/",
    "https://www.rudderstack.com/docs/profiles/dev-docs/commands/",
    "https://www.rudderstack.com/docs/profiles/additional-resources/glossary/",
]

DOC_FILES = []

FAQ_URLS = [
    "https://www.rudderstack.com/docs/profiles/additional-resources/faq/"
]

FAQ_FILES = [
    "data/faq.yaml"
]


def ensure_data_directory() -> None:
    """Ensure the data directory exists."""
    data_path = os.path.join(SCRIPT_DIR, DATA_DIR)
    os.makedirs(data_path, exist_ok=True)


def get_embedder() -> OpenAIEmbedData:
    """Create and return an embedding model instance."""
    return OpenAIEmbedData(model_name=EMBEDDING_MODEL)


def fetch_document(url: str) -> List[Document]:
    """
    Fetch and load content from a documentation URL.

    Args:
        url: The documentation URL to fetch

    Returns:
        List of Document objects containing the content
    """
    logger.info(f"Fetching document from {url}")
    loader = WebBaseLoader(url)
    return loader.load()


def extract_main_content(text: str) -> str:
    """
    Extract the main content from the page by removing headers and footers.

    Args:
        text: The full text content from the webpage

    Returns:
        Cleaned text with headers and footers removed
    """
    start_marker = "********.0"
    end_marker = "Was this page helpful?"

    start_pos = text.find(start_marker)
    if start_pos != -1:
        start_pos += len(start_marker)
    else:
        return text

    end_pos = text.find(end_marker, start_pos + 1)
    if end_pos == -1:
        return text[start_pos:]

    return text[start_pos:end_pos]


def split_into_chunks(content: str) -> List[str]:
    """
    Split the text content into smaller overlapping chunks for embedding.

    Args:
        content: The text content to split

    Returns:
        List of text chunks
    """
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=RAG_CHUNK_SIZE,
        chunk_overlap=RAG_CHUNK_OVERLAP
    )
    return text_splitter.split_text(content)


def get_storage_filename(url: str) -> str:
    """
    Generate a storage filename from a URL by converting it to a path-friendly format.

    Args:
        url: The URL to convert

    Returns:
        A filename suitable for storage
    """
    return url.rstrip("/").replace(BASE_URL, "").replace("/", "_")


def save_to_file(data: Any, filename: str) -> None:
    """
    Save data to a pickle file in the data directory.

    Args:
        data: The data to save
        filename: The name of the file (without extension)
    """
    ensure_data_directory()
    filepath = os.path.join(SCRIPT_DIR, DATA_DIR, f'{filename}.pkl')
    with open(filepath, 'wb') as f:
        pickle.dump(data, f)


def load_from_file(filename: str) -> Optional[Dict]:
    """
    Load data from a pickle file if it exists.

    Args:
        filename: The name of the file (without extension)

    Returns:
        The loaded data, or None if the file doesn't exist
    """
    filepath = os.path.join(SCRIPT_DIR, DATA_DIR, f'{filename}.pkl')

    if os.path.exists(filepath):
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    return None


def fetch_file_content(file_path: str) -> List[Document]:
    """
    Load and process content from a file.

    Args:
        file_path: Path to the file to load

    Returns:
        List of Document objects containing the content
    """
    logger.info(f"Loading content from file {file_path}")
    # file_full_path = os.path.join(SCRIPT_DIR, file_path)

    with open(file_path, 'r') as f:
        content = f.read()

    # Create a Document object similar to web content
    metadata = {"source": file_path}
    return [Document(page_content=content, metadata=metadata)]


def setup_embeddings(
    urls: List[str],
    files: List[str],
    collection_name: str
) -> None:
    """
    Process content from URLs and files, generate embeddings, and store in vector database.

    Args:
        urls: List of URLs to process
        files: List of files to process
        collection_name: Name of the vector DB collection to use
    """
    logger.info(f"Processing {collection_name} data...")

    vector_db = QdrantVectorDb()
    vector_db.initialize_collection(collection_name=collection_name, vector_size=RAG_VECTOR_SIZE)

    embedder = get_embedder()
    next_id = 1

    processed_docs = []

    # Process URLs
    for url in urls:
        filename = get_storage_filename(url)

        cached_data = load_from_file(filename)
        if cached_data:
            logger.info(f"Loaded cached data for {url}")
            processed_docs.append(cached_data)
            continue

        try:
            documents = fetch_document(url)

            for doc in documents:
                cleaned_content = extract_main_content(doc.page_content)
                text_chunks = split_into_chunks(cleaned_content)

                chunk_ids = []
                chunk_embeddings = []

                logger.info(f"Processing {len(text_chunks)} chunks for {url}")
                for chunk in text_chunks:
                    chunk_ids.append(next_id)
                    chunk_embeddings.append(embedder.embed(chunk))
                    next_id += 1

                document_data = {
                    "url": url,
                    "content": cleaned_content,
                    "ids": chunk_ids,
                    "chunks": text_chunks,
                    "embeddings": chunk_embeddings,
                    "metadata": doc.metadata
                }

                save_to_file(document_data, filename)
                processed_docs.append(document_data)

        except Exception as e:
            logger.error(f"Error processing {url}: {e}")

    # Process files
    for file_path in files:
        file_full_path = os.path.join(SCRIPT_DIR, file_path)
        filename = Path(file_full_path).stem
        logger.info(f"Processing file {filename}")

        cached_data = load_from_file(filename)
        if cached_data:
            logger.info(f"Loaded cached data for {file_path}")
            processed_docs.append(cached_data)
            continue

        try:
            documents = fetch_file_content(file_full_path)

            for doc in documents:
                text_chunks = split_into_chunks(doc.page_content)

                chunk_ids = []
                chunk_embeddings = []

                logger.info(f"Processing {len(text_chunks)} chunks for {file_path}")
                for chunk in text_chunks:
                    chunk_ids.append(next_id)
                    chunk_embeddings.append(embedder.embed(chunk))
                    next_id += 1

                document_data = {
                    "file": file_path,
                    "content": doc.page_content,
                    "ids": chunk_ids,
                    "chunks": text_chunks,
                    "embeddings": chunk_embeddings,
                    "metadata": doc.metadata
                }

                save_to_file(document_data, filename)
                processed_docs.append(document_data)

        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")

    logger.info(f"Adding {len(processed_docs)} documents to {collection_name} vector database")
    for doc in processed_docs:
        vector_db.add_documents(
            collection_name=collection_name,
            docs=doc['chunks'],
            embeddings=doc['embeddings'],
            ids=doc['ids']
        )

    logger.info(f"Successfully added {len(processed_docs)} documents to {collection_name} vector database")


def process_faqs() -> None:
    """
    Process FAQ data from the website, generate embeddings, and store them in the vector database.
    """
    setup_embeddings(
        urls=FAQ_URLS,
        files=FAQ_FILES,
        collection_name=FAQ_COLLECTION
    )


def process_documents() -> None:
    """
    Process all documents from the source URLs, generating embeddings
    and storing them locally and in the vector database.
    """
    setup_embeddings(
        urls=DOC_URLS,
        files=DOC_FILES,
        collection_name=DOCS_COLLECTION
    )


def main() -> None:
    """
    Main function to run the document and FAQ embedding generation process.
    """
    # Process FAQ data
    process_faqs()

    # Process documentation
    process_documents()

    logger.info("Successfully completed embedding generation")


if __name__ == "__main__":
    main()
