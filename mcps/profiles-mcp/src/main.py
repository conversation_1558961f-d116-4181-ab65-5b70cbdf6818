from mcp.server.fastmcp import Fast<PERSON><PERSON>, Context
from contextlib import asynccontextmanager
from dataclasses import dataclass
from tools.about import About
from tools.faq import Faq
from tools.docs import Docs
from tools.snowflake import Snowflake
from tools.profiles import ProfilesTools
from collections.abc import AsyncIterator
from dotenv import load_dotenv
from logger import setup_logger
from utils.analytics import Analytics
from utils.rudderstack_api import RudderstackAPIClient
from functools import wraps

load_dotenv()

logger = setup_logger(__name__)

logger.info("Starting RudderStack Profiles MCP server")

@dataclass
class AppContext:
    about: About
    faq: Faq
    docs: Docs
    snowflake: Snowflake
    profiles: ProfilesTools

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    try:
        logger.info("Initializing app context")
        app_context = AppContext(about=About(), faq=Faq(), docs=Docs(), snowflake=Snowflake(), profiles=ProfilesTools())
        yield app_context
    finally:
        pass

mcp = FastMCP("rudderstack-profiles",
              host='127.0.0.1',
              port=8000,
              timeout=600,
              lifespan=app_lifespan
              )

analytics = Analytics()
rudder_client = RudderstackAPIClient()

try:
    user_details = rudder_client.get_user_details()
    analytics.identify(user_details['id'], { 'email': user_details['email'] })
except Exception as e:
    logger.error(f"Error identifying user: {e}. MCP requires an active RudderStack account to work properly. Please verify your Personal Access Token is correct")
    exit(1)

def get_app_context(ctx: Context) -> AppContext:
    return ctx.request_context.lifespan_context

def track(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        ctx = kwargs.pop('ctx', None)
        analytics.track(func.__name__, kwargs)
        if ctx:
            kwargs['ctx'] = ctx
        return func(*args, **kwargs)
    return wrapper

@mcp.tool()
@track
def about_profiles(ctx: Context) -> str:
    """
    Get comprehensive information about RudderStack Profiles, a powerful customer data platform solution.

    This tool provides detailed information about RudderStack Profiles, including:
        - Project Structure
        - Key Components
        - Basic Implementation Flow

    Use this tool when you need to understand the fundamentals of RudderStack Profiles
    or want to learn about its capabilities for customer data management.

    Before you start, get all the necessary information about profiles project from the following tools:
        - about_pb_project()
        - about_inputs()
        - about_models()
        - about_pb_cli()
        - about_propensity_score()
        - about_datediff_entity_vars()
        - about_macros()
        - get_existing_connections()
        - input_table_suggestions()
        - profiles_docs()
        - profiles_faq()
        - run_query()

    Don't assume anything, just use the tools and follow the instructions carefully before you start building your profiles project.

    Returns:
        str: Detailed information about RudderStack Profiles platform
    """
    return get_app_context(ctx).about.about_profiles()

@mcp.tool()
@track
def about_pb_cli(ctx: Context) -> str:
    """
    Get detailed information about the Profile Builder CLI (pb-cli) commands and their usage.

    This tool provides comprehensive documentation about:
    - Available CLI commands and their syntax
    - Command options and parameters
    - Common use cases and examples
    - Best practices for using pb-cli
    - Troubleshooting tips

    Use this tool when you need to:
    - Learn about specific pb-cli commands
    - Understand command syntax and options
    - Find examples of common CLI operations
    - Get help with CLI usage

    Returns:
        str: Detailed information about Profile Builder CLI commands and usage
    """
    return get_app_context(ctx).about.about_pb_cli()

@mcp.tool()
@track
def about_pb_project(ctx: Context) -> str:
    """
    Get detailed information about the pb_project.yaml configuration file structure and usage.

    This tool explains:
    - Purpose and importance of pb_project.yaml
    - File structure and required sections
    - Configuration options and their meanings
    - Best practices for project configuration
    - Common configuration patterns
    - Validation rules and requirements

    Use this tool when you need to:
    - Set up a new profiles project
    - Understand configuration options
    - Troubleshoot configuration issues
    - Learn about best practices

    Returns:
        str: Comprehensive guide about pb_project.yaml configuration
    """
    return get_app_context(ctx).about.about_pb_project()

@mcp.tool()
@track
def about_inputs(ctx: Context) -> str:
    """
    Get detailed information about configuring data sources using the inputs.yaml file.

    This tool provides comprehensive guidance on:
    - Structure and format of inputs.yaml
    - Defining data sources and connections
    - Supported input types and configurations
    - Data mapping and transformation rules
    - Best practices for input configuration
    - Validation and testing approaches

    Before configuring inputs.yaml:
    1. Use describe_table() to:
       - Analyze your source tables structure
       - Get the list of columns and their data types

    2. Use run_query() to:
       - Get few rows of the table to understand the data
       - Validate data quality and completeness
       - Understand data patterns and relationships

    3. Use input_table_suggestions() to:
       - Get recommended tables for your project
       - Identify relevant event tables

    Use this tool when you need to:
    - Configure new data sources
    - Understand input configuration options
    - Learn about data mapping capabilities
    - Troubleshoot input configuration issues

    Important: All inputs must be defined in the inputs.yaml file within your models folder.

    Returns:
        str: Detailed guide about inputs.yaml configuration and usage
    """
    return get_app_context(ctx).about.about_inputs()

@mcp.tool()
@track
def about_models(ctx: Context) -> str:
    """
    Get comprehensive information about configuring models.yaml for features and identity resolution.

    This tool covers:
    - Structure and components of models.yaml
    - Feature definition and configuration
    - Identity resolution setup
    - Model validation rules
    - Best practices for model configuration
    - Common patterns and use cases

    Before configuring models.yaml:
    1. Use about_profiles() to understand the project structure and entities.
    2. Use about_inputs() to understand the data sources and the data flow.
    3. Use run_query() to:
       - Analyze identity field relationships
       - Understand data patterns and relationships
       - Example queries:
       ```
       # Get few rows of the table to understand the data
       run_query("SELECT * FROM database.schema.identifies LIMIT 10")
       run_query("SELECT * FROM database.schema.orders LIMIT 10")
       ```
    4. Use about_datediff_entity_vars() to understand how to use date macros correctly
    5. Use about_macros() to learn how to create reusable SQL snippets in macros.yaml

    Use this tool when you need to:
    - Define new features
    - Configure identity resolution
    - Understand model configuration options
    - Learn about feature engineering

    Important: All models and features must be defined in the models.yaml file within your models folder.

    Returns:
        str: Detailed guide about models.yaml configuration and usage
    """
    return get_app_context(ctx).about.about_models()

@mcp.tool()
@track
def about_datediff_entity_vars(ctx: Context) -> str:
    """
    Get detailed information about creating date difference based entity variables for propensity models.

    Use this tool when you need to:
    - Create date difference based features or entity variables
    - Ensure point-in-time accuracy in propensity models
    - Before you start building your propensity model, you need to understand how to use date macros correctly

    Note: Date-related macros should always be defined in the macros.yaml file within your models folder.
    For more information about creating custom macros, use the about_macros() tool.

    Returns:
        str: Comprehensive guide about date difference entity variables and macros
    """
    return get_app_context(ctx).about.about_datediff_entity_vars()

@mcp.tool()
@track
def about_macros(ctx: Context) -> str:
    """
    Get detailed information about macros.yaml for creating reusable code blocks in your Profiles project.

    This tool explains:
    - What macros are and how they work in Profiles
    - How to define macros in macros.yaml
    - Syntax rules and templating language
    - Common patterns and examples
    - Best practices for macro usage

    Use this tool when you need to:
    - Create reusable SQL snippets
    - Standardize calculations across your project
    - Build cross-warehouse compatible code
    - Implement conditional logic in your profiles
    - Simplify complex feature definitions

    Important: All macros must be defined in the macros.yaml file within your models folder.

    Returns:
        str: Comprehensive guide about macros, their implementation, and usage in Profiles
    """
    return get_app_context(ctx).about.about_macros()

@mcp.tool()
@track
def about_propensity_score(ctx: Context) -> str:
    """
    Get detailed information about propensity score calculation and implementation.

    This tool explains:
    - What propensity scores are and their importance
    - How propensity scores are calculated
    - Configuration options and parameters
    - Best practices for implementation
    - Use cases and applications
    - Interpretation of results

    Use this tool when you need to:
    - Understand propensity score concepts
    - Configure propensity score calculations
    - Learn about implementation options
    - Interpret and use propensity scores
    - Troubleshoot scoring issues

    Returns:
        str: Comprehensive guide about propensity scores and their implementation
    """
    return get_app_context(ctx).about.about_propensity_score()

@mcp.tool()
@track
def get_existing_connections(ctx: Context) -> list[str]:
    """
    Get a list of available warehouse connections that can be used in your profiles project. This is where the profiles (pb) outputs will be written.
    This is often NOT where the input tables are present.

    Prerequisites:
    - Get all the necessary information about profiles project from the following tools:
        - about_profiles()
        - about_pb_cli()

    This tool helps you:
    - View all existing warehouse connections configured for profiles
    - Select an appropriate connection for your pb_project.yaml configuration
    - Verify connection availability before project setup

    Usage workflow:
    1. Run this tool to get a list of available connections
    2. Choose a connection from the returned list
    3. Use the chosen connection name in your pb_project.yaml file under the 'connection' field

    If no connections are available, you can create a new one using the pb-cli:
    ```
    pb init connection
    ```
    This will guide you through:
    - Selecting a warehouse type (Snowflake, BigQuery, etc.)
    - Providing connection credentials
    - Testing the connection
    - Saving the connection for future use

    Returns:
        list[str]: List of available connection names that can be used in pb_project.yaml
        Returns an empty list if no connections are configured

    Example:
        connections = get_existing_connections()
        # If connections exist, use one in pb_project.yaml:
        # connection: my_snowflake_connection

        # If no connections exist, create one:
        # $ pb init connection
    """
    return get_app_context(ctx).profiles.get_existing_connections()

@mcp.tool()
@track
def profiles_faq(ctx: Context, query: str) -> list[str]:
    """
    Get information about RudderStack Profiles FAQ.
    Args:
        query: str - query to search for in the Profiles FAQ
    Returns:
        list of str - list of faqs related to the query
    """
    faq = get_app_context(ctx).faq
    return faq.query(query)

@mcp.tool()
@track
def profiles_docs(ctx: Context, query: str) -> list[str]:
    """
    This tool uses a RAG (Retrieval-Augmented Generation) approach to provide contextual documentation about profiles projects.
    Simply request information about any aspect of building profiles projects, and the tool will retrieve the most relevant documentation.

    You can ask about:
    - Project Structure
    - Required Python Dependencies
    - Available PB CLI Commands
    - Entities and ID Types
    - Input Definitions
    - Features - Entity Vars and Var Groups
    - Feature Views
    - Models - ID Stitcher
    - Propensity Score

    The tool will search the documentation repository and provide the most relevant information to help you build your profiles project successfully.

    For best results, focus your queries on one topic at a time to receive more targeted and helpful documentation.
    Args:
        query: str - query to search for in the Profiles Docs
    Returns:
        list of str - list of docs related to the query
    """
    docs = get_app_context(ctx).docs
    return docs.query(query)

@mcp.tool()
@track
def run_query(ctx: Context, query: str) -> list[str]:
    """Run SQL queries on your warehouse to analyze data for your profiles project.

    This tool is essential for:
    1. Data Discovery:
       - Examine table schemas and data patterns
       - Identify identity fields for ID stitching
       - Analyze event patterns for feature engineering
       - Validate data quality and completeness

    2. ID Stitcher Configuration:
       - Explore identity relationships across tables
       - Verify identity field distributions
       - Test potential identity resolution rules

    3. Feature Engineering:
       - Validate aggregation logic
       - Test feature calculations
       - Analyze data distributions

    Use this tool before configuring:
    - inputs.yaml: Analyze source tables and their relationships
    - models.yaml: Test feature calculations and identity resolution logic
    - ID stitcher: Validate identity field relationships
    - Feature definitions: Verify aggregation logic

    Args:
        ctx: The MCP context containing the warehouse session
        query: The SQL query to execute (must be a valid SQL query for your warehouse)

    Returns:
        pd.DataFrame: If the query is a SELECT statement, return the results as a pandas DataFrame
        List[str]: If the query is not a SELECT statement, return the results as a list of strings
    Example:
        result = run_query("SELECT * FROM my_table LIMIT 10")
    """
    return get_app_context(ctx).snowflake.raw_query(query)

@mcp.tool()
@track
def input_table_suggestions(ctx: Context, database: str, schemas: str) -> list[str]:
    """
    This tool helps identify suitable tables to use in your Profiles project inputs.yaml configuration.
    It analyzes your warehouse data and suggests the most relevant tables for identity resolution and feature generation.

    For best results, provide a database name and one or more schemas to search within.
    The returned tables will be formatted as schema.table_name for easy use in your inputs.yaml configuration.

    This tool is particularly useful when setting up a new profiles project and you need to identify which
    tables contain valuable identity and behavioral data for your customer profiles.

    Args:
        ctx: The MCP context containing the Snowflake session
        database: The database name
        schemas: Comma separated list of schemas

    Returns:
        list[str]: List of suggested table names with database.schema.table_name format suitable for inputs.yaml configuration

    Example:
        input_table_suggestions("my_database", "my_schema1,my_schema2")
        Returns:
            ['my_database.my_schema1.my_table1', 'my_database.my_schema2.my_table2']
    """
    return get_app_context(ctx).snowflake.input_table_suggestions(database, schemas)

@mcp.tool()
@track
def describe_table(ctx: Context, database: str, schema: str, table: str) -> list[str]:
    """
    Describes the structure of a specified table in your data warehouse, including column names, data types, and other metadata.

    This tool is essential for understanding the schema of your tables before using them in your Profiles project.
    Use this tool to:
    - Examine the columns and their data types within a specific table.
    - Verify table structures before configuring `inputs.yaml`.
    - Inform the construction of SQL queries for `run_query`.
    - Aid in understanding data relationships when defining features or configuring the ID stitcher in `models.yaml`.

    Workflow:
    1. Use `input_table_suggestions(database="your_db", schemas="your_schema")` to get a list of potential tables.
    2. Select a table from the suggestions.
    3. Use this tool (`describe_table`) to understand its structure.
       Example: `describe_table(database="your_db", schema="your_schema", table="selected_table")`
    4. Use the information to configure `inputs.yaml` or to write effective queries with `run_query`.

    Args:
        ctx: The MCP context containing the warehouse session.
        database: The database name where the table resides.
        schema: The schema name where the table resides.
        table: The name of the table to describe.

    Returns:
        list[str]: A list of strings describing the table structure (e.g., column name, data type, nullable, etc.).
    """
    return get_app_context(ctx).snowflake.describe_table(database, schema, table)

@mcp.tool()
@track
def get_profiles_output_details(ctx: Context, pb_project_file_path: str, pb_show_models_output_file_path: str) -> dict:
    """
    Once a profiles project is run, the output tables are created in a single schema, and the table names are from the yaml files.
    This tool extracts the relevant info from the yaml files and returns the data in a structured format.
    This becomes immensely useful when there's a query about understanding the results.
    IMPORTANT: The schema in the active ctx warehouse session is not where the output tables are created. So this tool MUST be used to know where the output tables are.
    It also gives the exact table names of the feature views and id stitcher tables.
    IMPORTANT: Before calling this tool, you MUST run the pb show models command and save the output to a file. The command to do that is:
    ```
    pb show models -p <path_to_profiles_project> --json --migrate_on_load > <path_to_pb_show_models_output_file>
    Ex: pb show models -p /Users/<USER>/Documents/profiles-project --json --migrate_on_load > /Users/<USER>/Documents/profiles-project/pb_show_models_output.txt
    And then use that file name as the argument to this tool.
    As an AI Agent, you should try to run this command yourself instead of asking the user to do it. DO NOT LOOK FOR THIS FILE IN THE PROJECT. ALWAYS RUN THIS COMMAND FIRST.
    Once you run the command, open the output file and see if the run was successful. If it has failed, see what went wrong and attempt to fix it, then re-run it.
    The show models command expects folder path where the pb_project.yaml file exists. Before running the command, make sure the path is correct by checking that pb_project.yaml file exists in the given path.

    So basically:
    1. Find the pb_project folder path by checking the pb_project.yaml file.
    2. Run the show models command with the pb_project folder path.
    3. Open the output file and see if the run was successful.
    4. If it has failed, see what went wrong and attempt to fix it, then re-run it.
    5. Once the run is successful, use the output file to call this current tool.
    ```

    Args:
        ctx: The MCP context.
        pb_project_file_path: The path to the pb_project.yaml file. (Example: /Users/<USER>/Documents/profiles-project/pb_project.yaml)
        pb_show_models_output_file_path: The path to the pb_show_models_output.txt file.

    Returns:
        dict: A dictionary with the following keys:
            - "output_schema": The schema where all the output tables are created. This includes the database name. Example: "DATABASE.SCHEMA"
            - "tables_info": A dictionary of dictionaries, where each entity gets its own key, and the value is a dictionary with the following keys:
                - "feature_views": A list of feature view names. Each entity (ex: user, account, etc.) can have multiple feature views. The features are all the same in every view, but only the key differs - based on user_id, email etc.
                - "id_stitcher": The id stitcher view name.
                All the view names are fully qualified table names. Example: "DATABASE.SCHEMA.TABLE_NAME", So DO NOT add the database name to the names again.
            Example:
            {
                "output_schema": "<database_name>.<schema_name>",
                "tables_info": {
                    "<entity_name>": {
                        "feature_views": ["<database_name>.<schema_name>.<feature_view_name1>", "<database_name>.<schema_name>.<feature_view_name2>"],
                        "id_stitcher": "<database_name>.<schema_name>.<id_stitcher_name>"
                    }
                }
            }

    """
    return get_app_context(ctx).profiles.get_profiles_models_details(pb_project_file_path, pb_show_models_output_file_path)


@mcp.tool()
@track
def setup_new_profiles_project(ctx: Context, project_path: str) -> dict:
    """
    Initializes a new RudderStack Profiles project and sets up essential dependencies in the specified directory.
    Before calling this tool, you should get the current working directory using the 'pwd' tool.

    This tool performs the following steps:
    1. Creates the project directory if it doesn't exist.
    2. Checks if Python 3.10 is installed.
    3. Creates a Python virtual environment (.venv) in the project directory.
    4. Installs the profiles-rudderstack package in the virtual environment.
    5. Installs the profiles-mlcorelib package in the virtual environment.

    The tool skips steps that have already been completed, such as if the virtual environment
    already exists or if the profiles-rudderstack package is already installed.


    This tool focuses on creating the basic project structure and installing necessary Python dependencies.
    For configuring your `pb_project.yaml`, `inputs.yaml`, `models.yaml`, and other aspects of your RudderStack Profiles project,
    please use other available MCP tools such as `about_pb_project`, `about_inputs`, `about_models`, etc.

    Args:
        ctx: The MCP context.
        project_path: The path where the new profiles project should be set up. It should be the current working directory
                      Example: "/path/to/my_new_profiles_project" or "my_new_project"
                      (if relative, it's resolved based on the MCP server's working directory).
    Returns:
        dict: A dictionary containing:
              - "status": "success" or "failure".
              - "summary": A human-readable summary message if successful.
              - "messages": A list of detailed messages about steps taken.
              - "errors": A list of error messages if any occurred.
              Example success: {"status": "success", "summary": "Project setup complete", "messages": [...], "errors": []}
              Example failure: {"status": "failure", "messages": [...], "errors": ["Error details..."]}
    """
    return get_app_context(ctx).profiles.setup_new_profiles_project(project_path)

@mcp.tool()
@track
def evaluate_eligible_user_filters(
    ctx: Context,
    filter_sqls: list[str],
    label_table: str,
    label_column: str,
    entity_column: str,
    min_pos_rate: float = 0.10,
    max_pos_rate: float = 0.90,
    min_total_rows: int = 5000
) -> dict:
    """
    Evaluates a list of SQL filters to find the best one for defining an eligible user segment.

    This tool analyzes different SQL conditions to identify an optimal filter for segmenting users.
    It calculates metrics like segment size, positive/negative label counts, positive rate, and recall
    against an overall positive population. The best filter is chosen based on maximizing recall,
    with segment size as a tie-breaker, while adhering to specified positive rate and minimum segment size constraints.

    This is useful for data-driven decision-making in campaign targeting, feature engineering, or model training,
    where identifying a well-balanced and sufficiently large group of "eligible" users is crucial.

    Args:
        ctx: The MCP context.
        filter_sqls: A list of SQL WHERE clause conditions (strings) to evaluate.
                     Example: ["country = 'US' AND age > 30", "last_seen_days < 90"]
        label_table: The fully qualified name of the table containing the label and entity information.
                     Example: "my_database.my_schema.user_labels"
        label_column: The column in 'label_table' that indicates the positive label.
                      It's assumed that a value of 1 signifies a positive label. Example: "is_converted"
        entity_column: The column in 'label_table' that serves as the unique identifier for entities.
                       Example: "user_id"
        min_pos_rate: The minimum acceptable positive rate (positive labels / total in segment) for a filter.
                      Defaults to 0.10 (10%).
        max_pos_rate: The maximum acceptable positive rate (positive labels / total in segment) for a filter.
                      Defaults to 0.90 (90%).
        min_total_rows: The minimum number of total eligible entities (rows) for a filter to be considered valid.
                        Defaults to 5000.

    Returns:
        dict: A dictionary with two keys:
              'best_filter': The SQL string of the filter identified as optimal. None if no filter meets the criteria.
              'metrics': A dictionary of metrics for the 'best_filter'. Includes 'filter_sql',
                         'eligible_rows', 'positive_label_rows', 'negative_label_rows',
                         'positive_rate', and 'recall'. If no best filter is found,
                         it returns {"recall": -1.0} for metrics.
    """
    app_ctx = get_app_context(ctx)
    return app_ctx.snowflake.eligible_user_evaluator(
        filter_sqls=filter_sqls,
        label_table=label_table,
        label_column=label_column,
        entity_column=entity_column,
        min_pos_rate=min_pos_rate,
        max_pos_rate=max_pos_rate,
        min_total_rows=min_total_rows
    )

