import os
import getpass
from collections import OrderedDict

ENV_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), ".env")

# Define environment variable groups with metadata for extensibility
ENV_GROUPS = OrderedDict([
    ("SNOW<PERSON>AKE", [
        {"name": "SNOWFLAKE_USER", "required": True, "secret": False, "help": None},
        {"name": "SNOWFLAKE_ACCOUNT", "required": True, "secret": False, "help": None},
        {"name": "SNOWFLAKE_WAREHOUSE", "required": True, "secret": False, "help": None},
        {"name": "SNOWFLAKE_DATABASE", "required": True, "secret": False, "help": None},
        {"name": "SNOWFLAKE_SCHEMA", "required": True, "secret": False, "help": None},
    ]),
    ("RUDDERSTACK", [
        {"name": "RUDDERSTACK_PAT", "required": True, "secret": True, "help": "Your RudderStack personal access token"},
    ]),
])

AUTH_METHODS = ["SNOW<PERSON>AKE_PASSWORD", "SNOWFLAKE_PRIVATE_KEY_FILE"]

def read_env_file(path):
    env = {}
    if os.path.exists(path):
        with open(path) as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#') or '=' not in line:
                    continue
                k, v = line.split('=', 1)
                env[k.strip()] = v.strip()
    return env

def prompt_var(var, current=None, secret=False, help_text=None, required=True):
    prompt = f"{var}"
    if help_text:
        prompt += f" ({help_text})"
    if current:
        prompt += f" [{current}]"
    prompt += ": "
    while True:
        if secret:
            val = getpass.getpass(prompt)
        else:
            val = input(prompt)
        if not val and current:
            return current
        if val:
            return val
        if not required:
            return ""
        print("This field is required.")

def main():
    env = read_env_file(ENV_FILE)
    env_exists = os.path.exists(ENV_FILE)

    # Check if any required variables are missing in the existing .env
    missing_required = []
    for group_name, var_metas in ENV_GROUPS.items():
        for var_meta in var_metas:
            var = var_meta["name"]
            if var_meta.get("required", True) and var not in env:
                missing_required.append(var)

    # Check auth method variables
    auth_method_configured = False
    if "SNOWFLAKE_PASSWORD" in env and env["SNOWFLAKE_PASSWORD"]:
        auth_method_configured = True
    elif "SNOWFLAKE_PRIVATE_KEY_FILE" in env and env["SNOWFLAKE_PRIVATE_KEY_FILE"]:
        auth_method_configured = True

    values = env.copy()

    # Prompt for Snowflake variables
    for var_meta in ENV_GROUPS["SNOWFLAKE"]:
        var = var_meta["name"]
        if not env_exists or var not in env or not env[var]:
            values[var] = prompt_var(
                var,
                current=env.get(var),
                secret=var_meta.get("secret", False),
                help_text=var_meta.get("help"),
                required=var_meta.get("required", True)
            )

    # Prompt for RudderStack variables
    for var_meta in ENV_GROUPS["RUDDERSTACK"]:
        var = var_meta["name"]
        if not env_exists or var not in env or not env[var]:
            values[var] = prompt_var(
                var,
                current=env.get(var),
                secret=var_meta.get("secret", False),
                help_text=var_meta.get("help"),
                required=var_meta.get("required", True)
            )

    # Auth method
    if not auth_method_configured:
        print("\nChoose Snowflake authentication method:")
        print("  1) SNOWFLAKE_PASSWORD")
        print("  2) SNOWFLAKE_PRIVATE_KEY_FILE")
        while True:
            choice = input("Enter 1 or 2: ").strip()
            if choice == "1":
                pw = prompt_var(
                    "SNOWFLAKE_PASSWORD",
                    current=env.get("SNOWFLAKE_PASSWORD"),
                    secret=True,
                    required=True
                )
                values["SNOWFLAKE_PASSWORD"] = pw
                values["SNOWFLAKE_PRIVATE_KEY_FILE"] = ""
                break
            elif choice == "2":
                pk = prompt_var(
                    "SNOWFLAKE_PRIVATE_KEY_FILE",
                    current=env.get("SNOWFLAKE_PRIVATE_KEY_FILE"),
                    help_text="Path to your private key file",
                    required=True
                )
                if not os.path.isfile(pk):
                    print(f"File not found: {pk}")
                    continue
                values["SNOWFLAKE_PRIVATE_KEY_FILE"] = pk
                values["SNOWFLAKE_PASSWORD"] = ""
                break
            else:
                print("Please enter 1 or 2.")
    else:
        # Keep existing auth method
        if "SNOWFLAKE_PASSWORD" in env and env["SNOWFLAKE_PASSWORD"]:
            values["SNOWFLAKE_PASSWORD"] = env["SNOWFLAKE_PASSWORD"]
            values["SNOWFLAKE_PRIVATE_KEY_FILE"] = ""
        else:
            values["SNOWFLAKE_PRIVATE_KEY_FILE"] = env["SNOWFLAKE_PRIVATE_KEY_FILE"]
            values["SNOWFLAKE_PASSWORD"] = ""

    # Write to .env
    print("\nWriting values to .env...")
    with open(ENV_FILE, "w") as f:
        for group_name, var_metas in ENV_GROUPS.items():
            for var_meta in var_metas:
                var = var_meta["name"]
                f.write(f"{var}={values[var]}\n")
        if values["SNOWFLAKE_PASSWORD"]:
            f.write(f"SNOWFLAKE_PASSWORD={values['SNOWFLAKE_PASSWORD']}\n")
        if values["SNOWFLAKE_PRIVATE_KEY_FILE"]:
            f.write(f"SNOWFLAKE_PRIVATE_KEY_FILE={values['SNOWFLAKE_PRIVATE_KEY_FILE']}\n")
    print(".env file created/updated successfully!\n")

if __name__ == "__main__":
    main()