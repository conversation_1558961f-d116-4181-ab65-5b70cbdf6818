#!/usr/bin/env python3

import json
import os
from pathlib import Path

def update_mcp_config():
    current_dir = Path(__file__).parent.parent
    start_script = current_dir / "scripts" / "start.sh"
    start_script = str(start_script.absolute())

    cursor_dir = Path.home() / ".cursor"
    mcp_file = cursor_dir / "mcp.json"

    cursor_dir.mkdir(exist_ok=True)

    default_config = {
        "mcpServers": {
            "profiles": {
                "command": start_script,
                "args": []
            }
        }
    }

    try:

        if mcp_file.exists():
            with open(mcp_file, 'r') as f:
                config = json.load(f)
        else:
            config = default_config

        if "mcpServers" not in config:
            config["mcpServers"] = {}

        config["mcpServers"]["profiles"] = {
            "command": start_script,
            "args": []
        }

        with open(mcp_file, 'w') as f:
            json.dump(config, f, indent=2)

        print(f"✓ Updated {mcp_file} successfully")
        return True

    except Exception as e:
        print(f"✗ Failed to update MCP configuration: {str(e)}")
        return False

if __name__ == "__main__":
    update_mcp_config()