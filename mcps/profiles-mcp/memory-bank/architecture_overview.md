# Profiles MCP Architecture Overview

## 1. High-Level Overview

Profiles MCP is a Model Context Protocol (MCP) server designed to facilitate building and managing RudderStack Profiles projects, leveraging AI-powered tools and direct Snowflake integration. It exposes a set of tools for project configuration, documentation retrieval, FAQ, and direct data warehouse operations, all orchestrated through a FastAPI-like async server (`FastMCP`).

---

## 2. Entry Point and Startup Flow

- **Entry Script:** [`scripts/start.sh`](../scripts/start.sh)
    - Activates the Python virtual environment
    - Runs the MCP server using `uv` and the main application: `src/main.py`

```mermaid
flowchart TD
    A[User/IDE] --> B[start.sh]
    B --> C[.venv Activation]
    C --> D[uv run mcp run src/main.py]
    D --> E[FastMCP Server Startup]
```

---

## 3. Main Application Structure

- **Main File:** [`src/main.py`](../src/main.py)
    - Initializes logging and environment
    - Defines `AppContext` (holds tool instances)
    - Sets up async lifespan context for dependency injection
    - Registers a set of tools (API endpoints) with the MCP server
    - Each tool is a function decorated with `@mcp.tool()` and `@track` (analytics)

### Key Tools/Endpoints
- `about_profiles`, `about_pb_cli`, `about_pb_project`, `about_inputs`, `about_models`, `about_propensity_score` (Documentation/Help)
- `get_existing_connections`, `input_table_suggestions`, `describe_table` (Warehouse/Project Utilities)
- `profiles_faq`, `profiles_docs` (RAG/FAQ/Docs Search)
- `run_query` (Direct SQL execution)

---

## 4. Core Components & Modules

```mermaid
flowchart TD
    subgraph Server
        main[main.py]
        logger[logger.py]
        constants[constants.py]
    end
    subgraph Tools
        about[tools/about.py]
        faq[tools/faq.py]
        docs[tools/docs.py]
        snowflake[tools/snowflake.py]
        profiles[tools/profiles.py]
    end
    subgraph Utils
        analytics[utils/analytics.py]
        embed[utils/embed.py]
        vectordb[utils/vectordb.py]
    end
    main --> about
    main --> faq
    main --> docs
    main --> snowflake
    main --> profiles
    main --> analytics
    about -->|Doc Strings| docs
    faq --> vectordb
    docs --> vectordb
    faq --> embed
    docs --> embed
    snowflake -->|Session| logger
    analytics --> logger
```

### Component Roles
- **main.py**: Orchestrates server, context, and tool registration
- **tools/**: Implements business logic for each tool/endpoint
- **utils/**: Provides analytics, embedding, and vector DB utilities
- **logger.py**: Centralized logging setup
- **constants.py**: Project-wide configuration constants

---

## 5. Data & Embeddings Flow

- **Embeddings** for documentation and FAQ content are generated as a **maintainer-only, one-time (or occasional) operation** (see `setup.py`).
- At **runtime**, the system loads the pre-generated `.pkl` files and `embeddings.zip` from `rag-search-service/data/` (not `src/data/`). **OpenAI and Qdrant are not required at runtime** unless remote Qdrant is explicitly configured.
- After running the indexing step, maintainers should copy or move the generated `.pkl` and `embeddings.zip` files to `rag-search-service/data/` for runtime use.
- **Note:** Most users and runtime environments do not need to run the embedding/indexing step. Only maintainers updating the documentation corpus should do this.

### Vector Database Abstraction & Qdrant Usage
- The system uses an abstract `VectorDb` class, with `QdrantVectorDb` as the concrete implementation.
- If the environment variable `QDRANT_BASE_URL` is **unset or empty**, the system uses an **in-memory Qdrant** instance, restored from `.pkl` files.
- If `QDRANT_BASE_URL` is set, it connects to a remote Qdrant instance at the specified URL.

### Fallback Logic with .pkl Files
- When using in-memory Qdrant, the system restores collections from cached `.pkl` files in `rag-search-service/data/`.
- Each `.pkl` file contains precomputed embeddings, document chunks, and IDs for a specific document set (e.g., FAQ, docs).
- On startup, the system loads these files and populates the in-memory Qdrant collections.

### Embedding Pipeline (OpenAI API)
- Embeddings are generated using the OpenAI API (see `embed.py`) **only during the maintainer indexing step**.
- The embedding model and vector size are configurable via `RAG_VECTOR_SIZE` (from `constants.py`).
- The OpenAI API key must be set in the environment as `OPENAI_API_KEY` **only for maintainers running the indexing step**.
- Embeddings are stored in `.pkl` files alongside the corresponding document chunks and IDs.
- The embedding pipeline supports both single and batch embedding (batching is currently a loop over single calls).

### Data Flow
1. **Embeddings Generation (Maintainer-only):**
   - Documents and FAQ content are embedded using OpenAI and saved as `.pkl` files (typically in a working directory such as `src/data/`).
   - This step is performed only by maintainers when updating the documentation/FAQ corpus.
2. **Startup/Restoration (Runtime):**
   - At runtime, the system loads the `.pkl` files and `embeddings.zip` from `rag-search-service/data/` (in-memory Qdrant) or connects to remote Qdrant if configured.
   - Maintainers must ensure the latest files are present in `rag-search-service/data/` after indexing.
3. **Querying (Runtime):**
   - At runtime, queries are embedded and compared against the stored vectors in Qdrant to retrieve the most similar documents.

### Setup and Dependencies
- The `setup.sh` script ensures all dependencies are installed and all required data files are present.
- If `embeddings.zip` is missing, it is downloaded and extracted into `rag-search-service/data/`.
- The script checks for Python 3.10 and other dependencies. **OpenAI and Qdrant are only required for maintainers running the indexing step.**

### Failure Modes & Edge Cases
- If `.pkl` files are missing or corrupted in `rag-search-service/data/`, in-memory Qdrant will not restore collections, and search will fail.
- If `QDRANT_BASE_URL` is set incorrectly, the system may not connect to the intended Qdrant instance.
- If the vector size in `.pkl` files does not match `RAG_VECTOR_SIZE`, collection creation or querying may fail.
- If the setup script is not run, dependencies or data may be missing.
- If `OPENAI_API_KEY` is not set, **embedding generation will fail for maintainers, but runtime is unaffected**.

### Best Practices
- Always run `setup.sh` after cloning or updating the repo to ensure all data and dependencies are in place.
- Keep `.pkl` files in sync with the embedding model and vector size used in the codebase.
- After indexing, always copy or move the generated files to `rag-search-service/data/` for runtime use.
- Document any changes to the embedding pipeline or data format in the memory bank.

### Data & Embeddings System Flow
```mermaid
flowchart TD
    subgraph Maintainer_Only
        setup["setup.py indexing"]
        embed[utils/embed.py]
        vectordb[utils/vectordb.py]
        openai[(OpenAI API)]
        data_pkl[data/*.pkl, data/faq.yaml, embeddings.zip]
        setup --> embed
        embed --> openai
        embed --> data_pkl
        vectordb --> data_pkl
    end
    subgraph Runtime
        faq[tools/faq.py]
        docs[tools/docs.py]
        vectordb_runtime[utils/vectordb.py]
        data_pkl_runtime[rag-search-service/data/*.pkl, rag-search-service/data/faq.yaml, rag-search-service/data/embeddings.zip]
        faq ==> vectordb_runtime
        docs ==> vectordb_runtime
        vectordb_runtime --> data_pkl_runtime
    end
    subgraph Optional_Remote_Qdrant
        decision_qdrant{QDRANT_BASE_URL set?}
        qdrant[(Qdrant Server)]
        vectordb_runtime --> decision_qdrant
        decision_qdrant -- "No (in-memory)" --> data_pkl_runtime
        decision_qdrant -- "Yes (remote)" --> qdrant
    end
```

**Legend:**
- Maintainer_Only: Indexing/embedding step, only for maintainers
- Runtime: Retrieval/search step, for all users and services
- Optional_Remote_Qdrant: Only if remote Qdrant is configured
- Dashed arrow: Setup/Indexing Flow
- Solid arrow: Retrieval Flow

---

## 6. Snowflake Integration

- **tools/snowflake.py** manages Snowflake session lifecycle, query execution, and table discovery
- Used by tools for direct data access, table suggestions, and schema introspection

---

## 7. Analytics & Observability

- **utils/analytics.py** tracks tool usage and user context via RudderStack
- All tool invocations are wrapped with `@track` for event logging
- **logger.py** provides file-based logging for server and tool operations

---

## 8. Extensibility & Customization

- New tools can be added by implementing a function, decorating with `@mcp.tool()` and (optionally) `@track`
- Embedding and vector DB backends are abstracted for easy replacement
- Data and documentation sources are modular (see `data/` and `setup.py`)

---

## 9. Example Request Flow

```mermaid
sequenceDiagram
    participant User
    participant IDE
    participant MCP as FastMCP Server
    participant Tool as Tool Handler
    participant Snowflake
    participant Qdrant
    User->>IDE: Natural language query
    IDE->>MCP: API call (e.g. run_query)
    MCP->>Tool: Route to tool function
    Tool->>Snowflake: (if needed) Query warehouse
    Tool->>Qdrant: (if needed) Retrieve docs/FAQ
    Tool-->>MCP: Return result
    MCP-->>IDE: Respond to user
```

---

## 10. Key Design Patterns

- **Async context management** for dependency injection and resource lifecycle
- **Decorator-based tool registration** for extensibility
- **Separation of concerns** between server, tools, and utilities
- **RAG (Retrieval-Augmented Generation)** for documentation and FAQ
- **Centralized configuration** via environment and constants

---

## 11. Directory Structure (Relevant Parts)

```
mcps/profiles-mcp/
├── scripts/
│   └── start.sh
├── src/
│   ├── main.py
│   ├── constants.py
│   ├── logger.py
│   ├── tools/
│   │   ├── about.py
│   │   ├── faq.py
│   │   ├── docs.py
│   │   ├── snowflake.py
│   │   └── profiles.py
│   ├── utils/
│   │   ├── analytics.py
│   │   ├── embed.py
│   │   └── vectordb.py
│   ├── data/
│   │   ├── *.pkl
│   │   └── faq.yaml
│   └── setup.py
└── memory-bank/
    └── architecture_overview.md (this file)
```

---

## 12. Notes
- This document is auto-generated for architectural clarity. Update as the codebase evolves. 